import json
import time
import random
import logging
import threading
from collections import deque
from tencentcloud.common import credential
from tencentcloud.common.profile.client_profile import ClientProfile
from tencentcloud.common.profile.http_profile import HttpProfile
from tencentcloud.common.exception.tencent_cloud_sdk_exception import TencentCloudSDKException
from tencentcloud.tmt.v20180321 import tmt_client, models

# 全局常量配置
SECRET_ID   = "AKIDTyOa1LSO8iATnzzmX9h3FF2d3FDyXv64"
SECRET_KEY  = "KWVItGRQGsHSQ0SjofLp6A0MbdS3SecM"
REGION      = "ap-beijing"
ENDPOINT    = "tmt.tencentcloudapi.com"
MAX_QPS     = 4  # 设置为比限制低一点，留出余量
MAX_RETRIES = 3  # 最大重试次数

class RateLimiter:
    """
    简单的令牌桶限流器
    """
    def __init__(self, max_rate=5):
        self.max_rate = max_rate  # 每秒最大请求数
        self.tokens = max_rate  # 初始令牌数
        self.last_refill_time = time.time()  # 上次令牌补充时间
        self.lock = threading.Lock()  # 用于线程安全
    
    def acquire(self):
        """
        获取一个令牌，如果没有可用令牌，则等待
        
        Returns:
            float: 等待时间（秒）
        """
        with self.lock:
            self._refill_tokens()
            
            if self.tokens < 1:
                # 计算需要等待的时间
                wait_time = (1.0 - self.tokens) / self.max_rate
                time.sleep(wait_time)
                self._refill_tokens()
            
            self.tokens -= 1
            return 0
    
    def _refill_tokens(self):
        """
        根据时间流逝补充令牌
        """
        now = time.time()
        elapsed = now - self.last_refill_time
        
        # 计算需要补充的令牌数
        new_tokens = elapsed * self.max_rate
        
        if new_tokens > 0:
            self.tokens = min(self.tokens + new_tokens, self.max_rate)
            self.last_refill_time = now

class TencentTranslator:
    def __init__(self):
        if not SECRET_ID or not SECRET_KEY:
            raise ValueError("请在模块顶部正确配置 SECRET_ID 和 SECRET_KEY")
        cred = credential.Credential(SECRET_ID, SECRET_KEY)
        http_profile = HttpProfile()
        http_profile.endpoint = ENDPOINT
        client_profile = ClientProfile()
        client_profile.httpProfile = http_profile
        self._client = tmt_client.TmtClient(cred, REGION, client_profile)
        self.logger = logging.getLogger("translator.TencentTranslator")
        
        # 初始化限流器
        self.rate_limiter = RateLimiter(MAX_QPS)
        
        # 初始化缓存，用于存储最近的翻译结果
        self.cache = {}
        self.cache_size = 1000  # 缓存大小

    def translate(self, text: str, source: str = "auto", target: str = "zh", project_id: int = 0) -> str:
        # 如果文本在缓存中，直接返回缓存结果
        cache_key = f"{text}:{source}:{target}"
        if cache_key in self.cache:
            return self.cache[cache_key]
            
        req = models.TextTranslateRequest()
        params = {
            "SourceText": text,
            "Source": source,
            "Target": target,
            "ProjectId": project_id
        }
        req.from_json_string(json.dumps(params, ensure_ascii=False))
        
        retries = 0
        max_retries = MAX_RETRIES
        result = ""
        
        while retries <= max_retries:
            try:
                # 获取令牌，必要时等待
                self.rate_limiter.acquire()
                
                # 发送请求
                resp = self._client.TextTranslate(req)
                result = json.loads(resp.to_json_string()).get("TargetText", "")
                
                # 成功获取结果，跳出重试循环
                if result:
                    # 添加到缓存
                    if len(self.cache) >= self.cache_size:
                        # 如果缓存已满，随机删除一些条目
                        keys_to_delete = random.sample(list(self.cache.keys()), self.cache_size // 4)
                        for key in keys_to_delete:
                            self.cache.pop(key, None)
                    self.cache[cache_key] = result
                    
                break
                
            except TencentCloudSDKException as e:
                if "RequestLimitExceeded" in str(e):
                    retries += 1
                    if retries <= max_retries:
                        # 指数退避重试
                        wait_time = (2 ** retries) + random.uniform(0, 1)
                        self.logger.warning(f"翻译请求频率超限，第{retries}次重试，等待{wait_time:.2f}秒")
                        time.sleep(wait_time)
                    else:
                        self.logger.error(f"翻译请求频率超限，重试{max_retries}次后仍失败: {e}")
                else:
                    self.logger.error(f"翻译异常: {e}, 原文: {text[:100]}...")
                    break
        
        if not result and text.strip():
            self.logger.warning(f"翻译结果为空，原文: {text[:100]}...")
            
        return result

# 在模块加载时就创建一个单例，供 translate_text 调用
_default_translator = TencentTranslator()

def translate_text(text: str) -> str:
    """
    将传入的文本翻译为中文，如果 text 为 None、空或仅包含空白，则直接返回空字符串。
    其它情况调用腾讯云翻译接口，如果翻译失败或结果为空，则返回原始文本。
    """
    if not text or not text.strip():
        return ""
    
    # 调用翻译接口
    translated = _default_translator.translate(text)
    
    # 如果翻译结果为空但原文不为空，则返回原文
    if not translated and text.strip():
        logger = logging.getLogger("translator")
        logger.warning(f"翻译结果为空，使用原文: {text[:100]}...")
        return text
        
    return translated

# 模块测试
if __name__ == "__main__":
    # 配置日志
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    
    samples = [None, "", "   ", "Hello, world!"]
    for s in samples:
        print(f"原文：{repr(s)} → 译文：{repr(translate_text(s))}")
        
    # 测试限流
    print("\n测试限流和重试机制:")
    for i in range(10):
        start = time.time()
        result = translate_text(f"Test sentence {i+1}")
        end = time.time()
        print(f"请求 {i+1}: 耗时 {end-start:.2f}秒, 结果: {result}")
