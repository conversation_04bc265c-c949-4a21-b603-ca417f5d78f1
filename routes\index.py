"""
首页相关路由
"""
import logging
from flask import Blueprint, render_template
from utils.db import get_connection
from models.task_config import TaskConfig
from services.scheduler_service import SchedulerService

# 创建蓝图
index_bp = Blueprint('index', __name__)

@index_bp.route('/')
def index():
    """首页"""
    # 获取统计信息
    conn = None
    cursor = None
    stats = {
        "task_count": 0,
        "enabled_task_count": 0,
        "batch_count": 0,
        "record_count": 0,
        "success_rate": 0,
        "recent_batches": []
    }
    
    try:
        conn = get_connection()
        cursor = conn.cursor()
        
        # 任务总数
        cursor.execute("SELECT COUNT(*) FROM capture_task_config")
        stats["task_count"] = cursor.fetchone()[0]
        
        # 启用的任务数
        cursor.execute("SELECT COUNT(*) FROM capture_task_config WHERE is_enabled = 1")
        stats["enabled_task_count"] = cursor.fetchone()[0]
        
        # 批次总数
        cursor.execute("SELECT COUNT(*) FROM capture_data_batch")
        stats["batch_count"] = cursor.fetchone()[0]
        
        # 记录总数：三张表相加
        cursor.execute("""
            SELECT 
                (SELECT COUNT(*) FROM CAPTURE_DATA_FDA) +
                (SELECT COUNT(*) FROM CAPTURE_DATA_RYBZSZH) +
                (SELECT COUNT(*) FROM CAPTURE_DATA_WANFANG_MAGAZINE)
            FROM dual
        """)
        stats["record_count"] = cursor.fetchone()[0]
        
        # 成功率
        cursor.execute(
            "SELECT ROUND(NVL(SUM(CASE WHEN status = 'SUCCESS' THEN 1 ELSE 0 END) / COUNT(*) * 100, 0), 2) "
            "FROM capture_data_batch"
        )
        stats["success_rate"] = cursor.fetchone()[0]
        
        # 最近批次
        cursor.execute(
            "SELECT * FROM ("
            "  SELECT b.batch_id, t.task_name, b.capture_time, b.record_count, b.status "
            "  FROM capture_data_batch b "
            "  LEFT JOIN capture_task_config t ON b.task_id = t.id "
            "  ORDER BY b.capture_time DESC"
            ") WHERE ROWNUM <= 5"
        )
        for row in cursor.fetchall():
            stats["recent_batches"].append({
                "batch_id": row[0],
                "task_name": row[1],
                "capture_time": row[2],
                "record_count": row[3],
                "status": row[4]
            })
    except Exception as e:
        logging.error(f"获取统计信息失败: {e}")
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()
    
    # 获取任务状态
    scheduler_service = SchedulerService.get_instance()
    jobs = scheduler_service.get_all_jobs()
    
    return render_template("index.html", stats=stats, jobs=jobs) 