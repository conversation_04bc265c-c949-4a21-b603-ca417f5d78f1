<div class="table-responsive">
    <table class="table table-hover align-middle">
        <thead>
            <tr>
                <th>序号</th>
                <th>标题</th>
                <th>发布日期</th>
                <th>期号</th>
                <th>发表年份</th>
                <th>来源</th>
                <th>操作</th>
            </tr>
        </thead>
        <tbody>
            {% for record in records %}
            <tr>
                <td>{{ loop.index }}</td>
                <td data-bs-toggle="tooltip" title="{{ record.title }}">{{ record.title|truncate(30) }}</td>
                <td>{{ record.publish_date }}</td>
                <td>{{ record.issue_num }}</td>
                <td>{{ record.publish_year }}</td>
                <td>
                    <span class="badge bg-light text-dark">{{ record.sources }}</span>
                </td>
                <td>
                    <button type="button" class="btn btn-sm btn-outline-primary view-details" 
                            data-record-id="{{ record.id }}" data-task-type="wanfang_magazine">
                        <i class="bi bi-eye"></i> 详情
                    </button>
                </td>
            </tr>
            {% else %}
            <tr>
                <td colspan="7" class="text-center py-4">
                    <div class="text-muted">
                        <i class="bi bi-inbox fs-2 d-block mb-2"></i>
                        暂无记录数据
                    </div>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div> 