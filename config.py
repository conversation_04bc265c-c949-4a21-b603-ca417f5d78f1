import os
from datetime import timedelta

# 应用配置
class Config:
    # Flask配置
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev_secret_key_12345'
    # DEBUG = os.environ.get('DEBUG') or True
    DEBUG = os.environ.get('DEBUG', 'True').lower() == 'true'

    # 数据库配置
    ORACLE_CONFIG = {
        "host": os.environ.get('DB_HOST') or "*************",
        "port": int(os.environ.get('DB_PORT') or 1521),
        "user": os.environ.get('DB_USER') or "BIZ_DATA_FETCH",
        "password": os.environ.get('DB_PASSWORD') or "4ZBWSuCdPQJN~vhCt2nkJy1_Ufxh5b",
        "service_name": os.environ.get('DB_SERVICE') or "orcl"
    }
    
    # 调度器配置
    SCHEDULER_API_ENABLED = True
    SCHEDULER_TIMEZONE = 'Asia/Shanghai'
    JOBS_MISFIRE_GRACE_TIME = 60  # 错过执行时间的容忍度(秒)
    JOBS_COALESCE = True  # 堆积的任务只执行一次
    
    # 批次号配置
    BATCH_PREFIX = 'DC'  # Data Capture
    BATCH_DATE_FORMAT = '%Y%m%d'
    
    # 数据抓取配置
    CAPTURE_DAYS = 30  # 默认抓取最近30天的数据
    REQUEST_TIMEOUT = 30  # 请求超时时间(秒)
    MAX_RETRIES = 3  # 最大重试次数
    RETRY_DELAY = 5  # 重试延迟(秒)
    
    # 日志配置
    LOG_LEVEL = os.environ.get('LOG_LEVEL') or 'INFO'
    LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    LOG_DATE_FORMAT = '%Y-%m-%d %H:%M:%S'
    
    # 其他配置
    DEFAULT_PAGE_SIZE = 20  # 默认分页大小 