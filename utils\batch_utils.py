import datetime
import random
import string
from config import Config

class BatchUtils:
    """
    批次工具类，用于生成和管理数据抓取批次号
    """
    
    @staticmethod
    def generate_batch_id(task_id=None):
        """
        生成批次号，格式为：前缀 + 日期 + 任务ID + 随机字符
        例如：DC20250815001R3A
        
        Args:
            task_id (int, optional): 任务ID，如果提供则包含在批次号中
            
        Returns:
            str: 生成的批次号
        """
        # 获取当前日期
        current_date = datetime.datetime.now().strftime(Config.BATCH_DATE_FORMAT)
        
        # 生成4位随机字符
        random_chars = ''.join(random.choices(string.ascii_uppercase + string.digits, k=4))
        
        # 构建批次号
        if task_id is not None:
            # 任务ID格式化为3位数字，不足前面补0
            task_id_str = str(task_id).zfill(3)
            batch_id = f"{Config.BATCH_PREFIX}{current_date}{task_id_str}{random_chars}"
        else:
            batch_id = f"{Config.BATCH_PREFIX}{current_date}{random_chars}"
            
        return batch_id
    
    @staticmethod
    def parse_batch_id(batch_id):
        """
        解析批次号，提取其中的信息
        
        Args:
            batch_id (str): 批次号
            
        Returns:
            dict: 包含解析结果的字典，如果解析失败则返回空字典
        """
        try:
            # 检查批次号格式
            if not batch_id.startswith(Config.BATCH_PREFIX):
                return {}
            
            # 提取日期部分
            date_part = batch_id[len(Config.BATCH_PREFIX):len(Config.BATCH_PREFIX)+8]
            batch_date = datetime.datetime.strptime(date_part, Config.BATCH_DATE_FORMAT).date()
            
            # 提取任务ID部分（如果存在）
            task_id = None
            if len(batch_id) >= len(Config.BATCH_PREFIX) + 8 + 3:
                task_id_part = batch_id[len(Config.BATCH_PREFIX)+8:len(Config.BATCH_PREFIX)+8+3]
                if task_id_part.isdigit():
                    task_id = int(task_id_part)
            
            return {
                "date": batch_date,
                "task_id": task_id
            }
        except (ValueError, IndexError):
            return {}
            
    @staticmethod
    def is_valid_batch_id(batch_id):
        """
        检查批次号是否有效
        
        Args:
            batch_id (str): 批次号
            
        Returns:
            bool: 批次号有效返回True，否则返回False
        """
        return bool(BatchUtils.parse_batch_id(batch_id)) 