/**
 * 数据抓取管理平台 - 主样式表
 */

:root {
    --primary-color: #26a69a;
    --primary-hover: #00897b;
    --secondary-color: #6c757d;
    --success-color: #66bb6a;
    --info-color: #4dd0e1;
    --warning-color: #ffb74d;
    --danger-color: #ef5350;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    --body-bg: #f8f9fa;
    --card-bg: #ffffff;
    --card-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --card-shadow-hover: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    --border-color: rgba(0, 0, 0, 0.125);
    --text-color: #212529;
    --text-muted: #6c757d;
    --transition-speed: 0.3s;
}

/* 暗黑模式变量 */
[data-bs-theme="dark"] {
    --primary-color: #4db6ac;
    --primary-hover: #26a69a;
    --body-bg: #212529;
    --card-bg: #2c3034;
    --card-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.2);
    --card-shadow-hover: 0 0.5rem 1rem rgba(0, 0, 0, 0.3);
    --border-color: rgba(255, 255, 255, 0.1);
    --text-color: #f8f9fa;
    --text-muted: #adb5bd;
}

/* 全局样式 */
body {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background-color: var(--body-bg);
    color: var(--text-color);
    transition: background-color var(--transition-speed), color var(--transition-speed);
}

.container-fluid {
    max-width: 1600px;
}

/* 导航栏样式 */
.navbar {
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: all var(--transition-speed);
}

.navbar-brand {
    font-weight: 600;
    letter-spacing: 0.5px;
}

.nav-link {
    position: relative;
    padding: 0.5rem 1rem;
    font-weight: 500;
    transition: all 0.2s;
}

.nav-link:hover {
    transform: translateY(-2px);
}

.nav-link i {
    margin-right: 0.25rem;
}

/* 卡片样式 */
.card {
    border: none;
    border-radius: 0.5rem;
    background-color: var(--card-bg);
    box-shadow: var(--card-shadow);
    transition: all var(--transition-speed);
    margin-bottom: 1.5rem;
    overflow: hidden;
}

.card:hover {
    box-shadow: var(--card-shadow-hover);
}

.card-header {
    background-color: rgba(0, 0, 0, 0.03);
    border-bottom: 1px solid var(--border-color);
    padding: 1rem 1.25rem;
    font-weight: 600;
}

[data-bs-theme="dark"] .card-header {
    background-color: rgba(255, 255, 255, 0.05);
}

/* 按钮样式 */
.btn {
    border-radius: 0.375rem;
    padding: 0.375rem 0.75rem;
    font-weight: 500;
    transition: all 0.2s;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--primary-hover);
    border-color: var(--primary-hover);
}

/* 自定义按钮颜色 */
.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-group-sm > .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    border-radius: 0.25rem;
}

/* 表格样式 */
.table {
    margin-bottom: 0;
    vertical-align: middle;
    background-color: var(--card-bg);
    color: var(--text-color);
    transition: background-color var(--transition-speed), color var(--transition-speed);
}

.table th {
    font-weight: 600;
    border-top: none;
    background-color: rgba(0, 0, 0, 0.02);
    white-space: nowrap;
    transition: background-color var(--transition-speed);
}

[data-bs-theme="dark"] .table th {
    background-color: rgba(255, 255, 255, 0.05);
}

[data-bs-theme="dark"] .table {
    color: var(--text-color);
    border-color: var(--border-color);
}

[data-bs-theme="dark"] .table td,
[data-bs-theme="dark"] .table th {
    border-color: var(--border-color);
}

/* 表格文本截断样式 */
.table td {
    max-width: 250px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    vertical-align: middle;
    transition: background-color var(--transition-speed), border-color var(--transition-speed);
}

.table td {
    vertical-align: middle;
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.03);
}

[data-bs-theme="dark"] .table-hover tbody tr:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

/* 徽章样式 */
.badge {
    font-weight: 500;
    padding: 0.35em 0.65em;
    border-radius: 0.375rem;
}

/* 表单样式 */
.form-control,
.form-select {
    border-radius: 0.375rem;
    padding: 0.5rem 0.75rem;
    border: 1px solid var(--border-color);
    background-color: var(--card-bg);
    color: var(--text-color);
    transition: all 0.2s;
}

.form-control:focus,
.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(38, 166, 154, 0.25);
}

/* 进度条样式 */
.progress {
    height: 1.5rem;
    background-color: rgba(0, 0, 0, 0.05);
    border-radius: 0.5rem;
    margin: 1rem 0;
    overflow: hidden;
}

[data-bs-theme="dark"] .progress {
    background-color: rgba(255, 255, 255, 0.05);
}

.progress-bar {
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    transition: width 0.6s ease;
}

/* 统计卡片样式 */
.stat-card {
    text-align: center;
    padding: 1.5rem;
    border-radius: 0.5rem;
    transition: all 0.3s;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-card h3 {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 0.5rem 0;
}

.stat-card h6 {
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 0.5rem;
}

.stat-card i {
    font-size: 2rem;
    opacity: 0.8;
}

/* 图表容器样式 */
.chart-container {
    position: relative;
    height: 250px;
    margin: 1rem 0;
}

/* 动画效果 */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.fade-in {
    animation: fadeIn 0.5s;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.3);
}

[data-bs-theme="dark"] ::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
}

[data-bs-theme="dark"] ::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
}

[data-bs-theme="dark"] ::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* 响应式调整 */
@media (max-width: 768px) {
    .btn-group-sm > .btn {
        padding: 0.2rem 0.4rem;
        font-size: 0.7rem;
    }
    
    .table td, .table th {
        padding: 0.5rem;
    }
    
    .stat-card h3 {
        font-size: 2rem;
    }
    
    .chart-container {
        height: 200px;
    }
}

/* 页脚样式 */
.footer {
    margin-top: auto;
    background-color: var(--card-bg);
    border-top: 1px solid var(--border-color);
    transition: all var(--transition-speed);
}

/* DataTables 自定义样式 */
.dataTables_wrapper .dataTables_length,
.dataTables_wrapper .dataTables_filter,
.dataTables_wrapper .dataTables_info,
.dataTables_wrapper .dataTables_processing,
.dataTables_wrapper .dataTables_paginate {
    color: var(--text-color);
    margin-bottom: 0.5rem;
}

.dataTables_wrapper .dataTables_paginate .paginate_button {
    border-radius: 0.25rem;
    transition: all 0.2s;
}

.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
    background: rgba(0, 0, 0, 0.05);
    border: 1px solid transparent;
}

[data-bs-theme="dark"] .dataTables_wrapper .dataTables_paginate .paginate_button:hover {
    background: rgba(255, 255, 255, 0.1);
}

/* 主题切换按钮动画 */
#theme-toggle {
    transition: all 0.3s;
}

#theme-toggle:hover {
    transform: rotate(30deg);
}

/* Jumbotron 样式 (Bootstrap 5 中已移除，需要自定义) */
.jumbotron {
    padding: 2rem 1rem;
    margin-bottom: 2rem;
    background-color: var(--card-bg);
    border-radius: 0.5rem;
    box-shadow: var(--card-shadow);
    transition: background-color var(--transition-speed), color var(--transition-speed);
}

@media (min-width: 576px) {
    .jumbotron {
        padding: 4rem 2rem;
    }
}

/* 代码块和特殊字符样式 */
code, pre, .code-block {
    background-color: rgba(0, 0, 0, 0.03);
    border-radius: 0.25rem;
    padding: 0.2rem 0.4rem;
    font-size: 0.875em;
    color: var(--text-color);
    border: 1px solid rgba(0, 0, 0, 0.05);
    transition: background-color var(--transition-speed), color var(--transition-speed), border-color var(--transition-speed);
}

[data-bs-theme="dark"] code,
[data-bs-theme="dark"] pre,
[data-bs-theme="dark"] .code-block {
    background-color: rgba(255, 255, 255, 0.05);
    color: var(--text-color);
    border-color: rgba(255, 255, 255, 0.1);
}

/* 表格样式增强 */
.table-sm {
    transition: background-color var(--transition-speed), color var(--transition-speed), border-color var(--transition-speed);
}

[data-bs-theme="dark"] .table-sm {
    background-color: var(--card-bg);
    color: var(--text-color);
    border-color: var(--border-color);
}

/* 详情弹窗样式 */
.modal-content {
    border-radius: 0.5rem;
    border: none;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    background-color: var(--card-bg);
    color: var(--text-color);
}

.modal-header {
    border-bottom: 1px solid var(--border-color);
    padding: 1rem 1.25rem;
}

.modal-footer {
    border-top: 1px solid var(--border-color);
    padding: 1rem 1.25rem;
}

.modal-body {
    padding: 1.5rem;
}

[data-bs-theme="dark"] .modal-content {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.3);
}

/* 详情内容样式 */
#recordDetailContent p {
    margin-bottom: 0.5rem;
    white-space: normal;
    word-break: break-word;
    overflow-wrap: break-word;
}

#recordDetailContent .card {
    margin-bottom: 1rem;
}

#recordDetailContent .card-header {
    padding: 0.75rem 1rem;
}

#recordDetailContent .card-body {
    padding: 1rem;
    max-height: none;
    overflow-y: visible;
}

/* 长文本内容区域 */
.long-text-content {
    max-height: none;
    overflow-y: visible;
    padding: 0.5rem;
    border-radius: 0.25rem;
    background-color: rgba(0, 0, 0, 0.02);
    white-space: pre-wrap;
}

[data-bs-theme="dark"] .long-text-content {
    background-color: rgba(255, 255, 255, 0.05);
}

/* 加载中动画样式 */
#loadingSpinner {
    padding: 2rem 0;
} 

/* 卡片内表格样式 */
.card .table {
    background-color: transparent;
}

.card .table-sm {
    background-color: transparent;
}

[data-bs-theme="dark"] .card .table,
[data-bs-theme="dark"] .card .table-sm {
    background-color: transparent;
}

/* 列表样式增强 */
.card ul, .card ol {
    color: var(--text-color);
    transition: color var(--transition-speed);
}

[data-bs-theme="dark"] .card ul,
[data-bs-theme="dark"] .card ol {
    color: var(--text-color);
}


div.dataTables_wrapper div.dataTables_length select{
    width: 70px !important;
}