import datetime
from utils.db import get_connection
from utils.batch_utils import BatchUtils

class DataBatch:
    """
    数据批次模型，对应数据库表 CAPTURE_DATA_BATCH
    """
    
    STATUS_SUCCESS = "SUCCESS"
    STATUS_FAILED = "FAILED"
    STATUS_RUNNING = "RUNNING"
    
    def __init__(self, batch_id=None, task_id=None, capture_time=None, record_count=0, status=None, remark=None):
        """
        初始化数据批次对象
        
        Args:
            batch_id (str, optional): 批次ID
            task_id (int, optional): 任务ID
            capture_time (datetime, optional): 抓取时间
            record_count (int, optional): 记录数量
            status (str, optional): 状态
            remark (str, optional): 备注
        """
        self.batch_id = batch_id
        self.task_id = task_id
        self.capture_time = capture_time or datetime.datetime.now()
        self.record_count = record_count
        self.status = status
        self.remark = remark
    
    @staticmethod
    def create_new_batch(task_id):
        """
        创建新的数据批次
        
        Args:
            task_id (int): 任务ID
            
        Returns:
            DataBatch: 新创建的数据批次对象
        """
        batch_id = BatchUtils.generate_batch_id(task_id)
        return DataBatch(
            batch_id=batch_id,
            task_id=task_id,
            capture_time=datetime.datetime.now(),
            record_count=0,
            status=DataBatch.STATUS_RUNNING
        )
    
    @staticmethod
    def get_by_id(batch_id):
        """
        根据批次ID获取数据批次
        
        Args:
            batch_id (str): 批次ID
            
        Returns:
            DataBatch: 数据批次对象，如果不存在则返回None
        """
        conn = None
        cursor = None
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute(
                "SELECT batch_id, task_id, capture_time, record_count, status, remark "
                "FROM capture_data_batch WHERE batch_id = :batch_id",
                {"batch_id": batch_id}
            )
            row = cursor.fetchone()
            if row:
                return DataBatch(
                    batch_id=row[0],
                    task_id=row[1],
                    capture_time=row[2],
                    record_count=row[3],
                    status=row[4],
                    remark=row[5]
                )
            return None
        finally:
            if cursor:
                cursor.close()
            if conn:
                conn.close()
    
    @staticmethod
    def get_by_task_id(task_id, limit=10):
        """
        根据任务ID获取数据批次列表
        
        Args:
            task_id (int): 任务ID
            limit (int, optional): 返回的最大记录数，默认为10
            
        Returns:
            list: 数据批次对象列表
        """
        conn = None
        cursor = None
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute(
                "SELECT batch_id, task_id, capture_time, record_count, status, remark "
                "FROM capture_data_batch "
                "WHERE task_id = :task_id "
                "ORDER BY capture_time DESC",
                {"task_id": task_id}
            )
            
            batches = []
            for row in cursor.fetchall()[:limit]:
                batch = DataBatch(
                    batch_id=row[0],
                    task_id=row[1],
                    capture_time=row[2],
                    record_count=row[3],
                    status=row[4],
                    remark=row[5]
                )
                batches.append(batch)
            
            return batches
        finally:
            if cursor:
                cursor.close()
            if conn:
                conn.close()
    
    def save(self):
        """
        保存数据批次到数据库
        
        Returns:
            bool: 保存成功返回True，否则返回False
        """
        conn = None
        cursor = None
        try:
            conn = get_connection()
            cursor = conn.cursor()
            
            # 检查是否已存在
            cursor.execute(
                "SELECT 1 FROM capture_data_batch WHERE batch_id = :batch_id",
                {"batch_id": self.batch_id}
            )
            exists = cursor.fetchone() is not None
            
            if not exists:
                # 插入新记录
                cursor.execute(
                    "INSERT INTO capture_data_batch (batch_id, task_id, capture_time, record_count, status, remark) "
                    "VALUES (:batch_id, :task_id, :capture_time, :record_count, :status, :remark)",
                    {
                        "batch_id": self.batch_id,
                        "task_id": self.task_id,
                        "capture_time": self.capture_time,
                        "record_count": self.record_count,
                        "status": self.status,
                        "remark": self.remark
                    }
                )
            else:
                # 更新现有记录
                cursor.execute(
                    "UPDATE capture_data_batch SET "
                    "task_id = :task_id, "
                    "capture_time = :capture_time, "
                    "record_count = :record_count, "
                    "status = :status, "
                    "remark = :remark "
                    "WHERE batch_id = :batch_id",
                    {
                        "task_id": self.task_id,
                        "capture_time": self.capture_time,
                        "record_count": self.record_count,
                        "status": self.status,
                        "remark": self.remark,
                        "batch_id": self.batch_id
                    }
                )
            
            conn.commit()
            return True
        except Exception as e:
            if conn:
                conn.rollback()
            print(f"保存数据批次失败: {e}")
            return False
        finally:
            if cursor:
                cursor.close()
            if conn:
                conn.close()
    
    def delete(self):
        """
        从数据库中删除数据批次
        
        Returns:
            bool: 删除成功返回True，否则返回False
        """
        conn = None
        cursor = None
        try:
            conn = get_connection()
            cursor = conn.cursor()
            
            cursor.execute(
                "DELETE FROM capture_data_batch WHERE batch_id = :batch_id",
                {"batch_id": self.batch_id}
            )
            
            conn.commit()
            return True
        except Exception as e:
            if conn:
                conn.rollback()
            print(f"删除数据批次失败: {e}")
            return False
        finally:
            if cursor:
                cursor.close()
            if conn:
                conn.close()
                
    def to_dict(self):
        """
        将对象转换为字典
        
        Returns:
            dict: 包含对象属性的字典
        """
        return {
            "batch_id": self.batch_id,
            "task_id": self.task_id,
            "capture_time": self.capture_time,
            "record_count": self.record_count,
            "status": self.status,
            "remark": self.remark
        } 