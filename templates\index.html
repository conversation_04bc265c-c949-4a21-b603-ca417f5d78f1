{% extends 'base.html' %}

{% block title %}首页 - 数据抓取管理平台{% endblock %}

{% block content %}
<div class="jumbotron rounded-3 p-5 mb-4 fade-in">
    <div class="container-fluid py-3">
        <h1 class="display-5 fw-bold">数据抓取管理平台</h1>
        <p class="col-md-8 fs-4">集中管理和监控数据抓取任务，实现自动化数据采集</p>
        <div class="d-flex gap-2 mt-4">
            <a href="{{ url_for('tasks.task_list') }}" class="btn btn-primary btn-lg px-4">
                <i class="bi bi-list-task me-2"></i> 管理任务
            </a>
            <a href="{{ url_for('batches.batch_list') }}" class="btn btn-outline-secondary btn-lg px-4">
                <i class="bi bi-collection me-2"></i> 查看批次
            </a>
        </div>
    </div>
</div>

<div class="row row-cols-1 row-cols-md-4 g-4 mb-4 fade-in">
    <div class="col">
        <div class="card h-100 stat-card">
            <div class="card-body">
                <i class="bi bi-list-task text-primary"></i>
                <h3>{{ stats.task_count }}</h3>
                <h6>任务总数</h6>
                <p class="text-muted">已配置 {{ stats.enabled_task_count }} 个启用任务</p>
            </div>
        </div>
    </div>
    <div class="col">
        <div class="card h-100 stat-card">
            <div class="card-body">
                <i class="bi bi-collection text-success"></i>
                <h3>{{ stats.batch_count }}</h3>
                <h6>批次总数</h6>
                <p class="text-muted">共采集 {{ stats.record_count }} 条记录</p>
            </div>
        </div>
    </div>
    <div class="col">
        <div class="card h-100 stat-card">
            <div class="card-body">
                <i class="bi bi-check2-circle text-info"></i>
                <h3>{{ stats.success_rate }}%</h3>
                <h6>成功率</h6>
                <p class="text-muted">批次执行成功率</p>
            </div>
        </div>
    </div>
    <div class="col">
        <div class="card h-100 stat-card">
            <div class="card-body">
                <i class="bi bi-activity text-warning"></i>
                <h3>{{ jobs|length }}</h3>
                <h6>活动任务</h6>
                <p class="text-muted">当前调度器中的任务数</p>
            </div>
        </div>
    </div>
</div>

<div class="row fade-in">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">最近批次</h5>
                <a href="{{ url_for('batches.batch_list') }}" class="btn btn-sm btn-outline-primary">
                    查看全部 <i class="bi bi-arrow-right ms-1"></i>
                </a>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover align-middle">
                        <thead>
                            <tr>
                                <th>批次ID</th>
                                <th>任务名称</th>
                                <th>抓取时间</th>
                                <th>记录数</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for batch in stats.recent_batches %}
                            <tr>
                                <td>
                                    <a href="{{ url_for('batches.batch_detail', batch_id=batch.batch_id) }}" class="text-decoration-none fw-bold">
                                        {{ batch.batch_id }}
                                    </a>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <i class="bi bi-collection text-primary me-2"></i>
                                        {{ batch.task_name }}
                                    </div>
                                </td>
                                <td>{{ batch.capture_time|datetime }}</td>
                                <td>
                                    <span class="badge bg-light text-dark">{{ batch.record_count }}</span>
                                </td>
                                <td>
                                    {% if batch.status == 'SUCCESS' %}
                                    <span class="badge bg-success">
                                        <i class="bi bi-check-circle me-1"></i> 成功
                                    </span>
                                    {% elif batch.status == 'FAILED' %}
                                    <span class="badge bg-danger">
                                        <i class="bi bi-x-circle me-1"></i> 失败
                                    </span>
                                    {% elif batch.status == 'RUNNING' %}
                                    <span class="badge bg-warning text-dark">
                                        <i class="bi bi-hourglass-split me-1"></i> 运行中
                                    </span>
                                    {% else %}
                                    <span class="badge bg-secondary">
                                        <i class="bi bi-question-circle me-1"></i> {{ batch.status }}
                                    </span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{{ url_for('batches.batch_detail', batch_id=batch.batch_id) }}" class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-eye"></i> 详情
                                    </a>
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="6" class="text-center py-4">
                                    <div class="text-muted">
                                        <i class="bi bi-inbox fs-2 d-block mb-2"></i>
                                        暂无批次数据
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 初始化图表
        if (typeof Chart !== 'undefined') {
            initCharts();
        }
    });
</script>
{% endblock %}
