import requests
import math
import logging
from bs4 import BeautifulSoup
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from datetime import datetime
from dateutil.relativedelta import relativedelta
from crawlers.base_crawler import BaseCrawler
from models.data_rybzszh import DataRYBZSZH
from config import Config

class RYBZSZHCrawler(BaseCrawler):
    """
    食品抽检信息查询分析系统爬虫
    """
    
    # 登录信息
    LOGIN_URL = "https://std.nctid.cn/home/<USER>/login.html"
    ACCOUNT = "<EMAIL>"
    PASSWORD = "fagui@001"
    
    # 请求头
    HEADERS = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/115.0.0.0 Safari/537.36",
        "Accept": "*/*",
        "Referer": "https://std.nctid.cn/",
        "Origin": "https://std.nctid.cn",
        "Accept-Language": "zh-CN,zh;q=0.9",
        "Connection": "keep-alive"
    }
    
    def __init__(self, task_id, batch_id=None, days=None):
        """
        初始化食品抽检信息查询分析系统爬虫
        
        Args:
            task_id (int): 任务ID
            batch_id (str, optional): 批次ID，如果不提供则自动生成
            days (int, optional): 抓取最近多少天的数据，默认使用配置值
        """
        super().__init__(task_id, batch_id)
        self.days = days or 30  # 默认抓取最近30天的数据
        self.logger = logging.getLogger("crawler.RYBZSZHCrawler")
        
        # 设置会话
        self.session.headers.update(self.HEADERS)
        
        # 最大工作线程数
        self.max_workers = 10
    
    def _crawl(self):
        """
        执行抓取逻辑
        
        Returns:
            bool: 抓取成功返回True，否则返回False
        """
        try:
            # 登录
            if not self._login():
                self.logger.error("登录失败")
                return False
                
            # 设置时间范围
            today = datetime.today()
            etime = today.strftime("%Y-%m-%d")
            stime = (today - relativedelta(months=1)).strftime("%Y-%m-%d")
            
            # 获取所有项目ID
            all_items = self._get_all_items(stime, etime)
            if not all_items:
                self.logger.warning("未获取到任何项目ID")
                return False
                
            self.logger.info(f"共获取 {len(all_items)} 条项目ID，开始抓取详情...")
            
            # 抓取详情
            all_details = self._fetch_details(all_items)
            if not all_details:
                self.logger.warning("未获取到任何详情数据")
                return False
                
            # 保存数据
            self._save_records(all_details)
            
            return True
        except Exception as e:
            self.logger.error(f"抓取失败: {e}")
            return False
    
    def _login(self):
        """
        登录系统
        
        Returns:
            bool: 登录成功返回True，否则返回False
        """
        login_data = {
            "account": self.ACCOUNT,
            "password": self.PASSWORD
        }
        
        try:
            resp = self.session.post(self.LOGIN_URL, data=login_data)
            if resp.status_code != 200 or resp.json().get("code") != 1:
                self.logger.error("登录失败")
                return False
            return True
        except Exception as e:
            self.logger.error(f"登录异常: {e}")
            return False
    
    def _get_all_items(self, stime, etime, page_size=20):
        """
        获取所有项目ID
        
        Args:
            stime (str): 开始时间，格式为YYYY-MM-DD
            etime (str): 结束时间，格式为YYYY-MM-DD
            page_size (int, optional): 每页记录数，默认为20
            
        Returns:
            list: 项目列表
        """
        # 公共参数
        base_payload = {
            'catid': '89,90,94,95,96,97,98,99,100,102,622,239,119,120,121,122,123,124,125',
            'hege': '2',
            'stime': stime,
            'etime': etime,
            'page_size': str(page_size),
            'active': 'cn'
        }
        
        # 获取总数
        count_url = "https://std.nctid.cn/cj/search/getcounts.html"
        count_resp = self.session.post(count_url, data=base_payload)
        if count_resp.status_code != 200:
            self.logger.error(f"获取总数失败: {count_resp.text}")
            return []
            
        total_count = int(count_resp.json().get("data", {}).get("count", 0))
        if total_count == 0:
            return []
            
        total_pages = math.ceil(total_count / page_size)
        
        # 抓取所有项目ID
        all_items = []
        data_url = "https://std.nctid.cn/cj/search/cnselect.html"
        
        for page in range(1, total_pages + 1):
            payload = base_payload.copy()
            payload["page_num"] = str(page)
            resp = self.session.post(data_url, data=payload)
            if resp.status_code != 200:
                self.logger.warning(f"获取第{page}页数据失败: {resp.text}")
                continue
                
            items = resp.json().get("data", {}).get("data", [])
            all_items.extend(items)
        
        return all_items
    
    def _parse_detail(self, itemid, base_info):
        """
        解析详情页
        
        Args:
            itemid (str): 项目ID
            base_info (dict): 基础信息
            
        Returns:
            dict: 详情数据
        """
        url = f"https://std.nctid.cn/cj/search/lookinfo.html?active=cn&id={itemid}"
        resp = self.session.get(url)
        if resp.status_code != 200:
            self.logger.warning(f"获取详情失败: {resp.text}")
            return None
            
        soup = BeautifulSoup(resp.text, "html.parser")
        
        table = soup.select_one("table.layui-table")
        if not table:
            self.logger.warning(f"未找到详情表格: {itemid}")
            return None
            
        data = {
            "item_id": itemid,
            "source": "食品抽检信息查询分析系统",
            "country": "中国",
            "origin_country": "中国"
        }
        
        # 临时变量：用于拼接 不合格项目（标准值）
        reason = ""
        standard_limit = ""
        judge_result = ""  # 判定结果
        
        for tr in table.select("tr"):
            tds = tr.find_all("td")
            if len(tds) < 2:
                continue
            key = tds[0].get_text(strip=True).replace("：", "").replace(" ", "")
            value_cell = tds[1]
            value = value_cell.get_text(strip=True)
            
            # 处理链接字段
            if "来源链接" in key:
                a = value_cell.find("a")
                if a and a.has_attr("href"):
                    data["source_url"] = a["href"]
            elif "伙伴网链接" in key:
                a = value_cell.find("a")
                if a and a.has_attr("href"):
                    data["risk_platform_url"] = a["href"]
            
            # 字段匹配填入
            if key == "产品分类":
                data["category"] = value.split("-->")[0].strip()
            elif key == "产品名称":
                data["product_name"] = value
            elif key == "规格":
                data["spec_model"] = value
            elif key == "生产时间":
                data["production_date"] = value
            elif key == "生产企业名称":
                data["company_name"] = value
            elif key == "不合格原因":
                reason = value
                data["risk_factor"] = value
            elif key == "检测结果":
                data["test_result"] = value
            elif key == "标准/法规限值":
                standard_limit = value
            elif key == "通报时间":
                data["publish_date"] = value
            elif key == "判定结果":
                judge_result = value
        
        # 拼接字段
        if reason and standard_limit:
            data["unqualified_item_standard"] = f"{reason}（{standard_limit}）"
        
        # 构造任务标题与摘要
        tbdw = base_info.get("tbdw", "")
        scname = base_info.get("scname", "")
        title = base_info.get("title", "")
        hege = base_info.get("hege", "")
        data["title"] = f"{tbdw}通报{scname}生产的{title}{reason}{hege}"
        data["summary"] = data["title"]
        
        return data
    
    def _parse_detail_safe(self, item):
        """
        安全解析详情页
        
        Args:
            item (dict): 项目信息
            
        Returns:
            dict: 详情数据，如果解析失败则返回None
        """
        itemid = item.get("itemid")
        try:
            detail = self._parse_detail(itemid, item)
            self.logger.debug(f"成功抓取 itemid={itemid}")
            return detail
        except Exception as e:
            self.logger.error(f"抓取失败 itemid={itemid}，原因：{e}")
            return None
    
    def _fetch_details(self, items):
        """
        抓取所有详情
        
        Args:
            items (list): 项目列表
            
        Returns:
            list: 详情数据列表
        """
        all_details = []
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            futures = [executor.submit(self._parse_detail_safe, item) for item in items]
            
            for future in as_completed(futures):
                result = future.result()
                if result:
                    all_details.append(result)
        
        return all_details
    
    def _save_records(self, records):
        """
        保存记录到数据库
        
        Args:
            records (list): 记录列表
            
        Returns:
            tuple: (成功数量, 失败数量)
        """
        if not records:
            return 0, 0
            
        # 转换为DataRYBZSZH对象
        data_records = []
        for record in records:
            data_record = DataRYBZSZH(
                batch_id=self.batch.batch_id,
                task_id=self.task_id,
                **record
            )
            data_records.append(data_record)
            
        # 批量插入
        success_count, fail_count = DataRYBZSZH.bulk_insert(data_records)
        
        # 更新统计信息
        self.stats["success_count"] += success_count
        self.stats["failed_count"] += fail_count
        self.stats["total_count"] += len(records)
        
        return success_count, fail_count 