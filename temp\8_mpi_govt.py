#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新西兰初级产业部食品召回页面抓取脚本
抓取https://www.mpi.govt.nz/food-safety-home/food-recalls-and-complaints/recalled-food-products/页面的召回产品信息
"""

import requests
from bs4 import BeautifulSoup
import time
from urllib.parse import urljoin
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


class MPIFoodRecallsScraper:
    def __init__(self):
        self.base_url = "https://www.mpi.govt.nz"
        self.target_url = "https://www.mpi.govt.nz/food-safety-home/food-recalls-and-complaints/recalled-food-products/"
        self.session = requests.Session()

        # 设置请求头，模拟浏览器访问
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })

    def fetch_page(self, url):
        """获取网页内容"""
        try:
            response = self.session.get(url, timeout=30, verify=False)
            response.raise_for_status()
            response.encoding = 'utf-8'
            return response.text
        except requests.RequestException as e:
            return None

    def parse_recall_content(self, html_content):
        """解析召回产品内容"""
        soup = BeautifulSoup(html_content, 'html.parser')
        recall_items = []

        # 查找所有包含wrapper optional-sidebar类的div
        wrapper_divs = soup.find_all('div', class_='wrapper optional-sidebar')

        for wrapper_div in wrapper_divs:
            # 在每个wrapper div中查找richtext div
            richtext_div = wrapper_div.find('div', class_='richtext')

            if richtext_div:
                # 在richtext div中查找所有ul元素
                ul_elements = richtext_div.find_all('ul')

                for ul in ul_elements:
                    # 查找ul前面的h2标题（年份信息）
                    year_header = None
                    prev_sibling = ul.find_previous_sibling()
                    while prev_sibling:
                        if prev_sibling.name == 'h2':
                            year_header = prev_sibling.get_text().strip()
                            break
                        prev_sibling = prev_sibling.find_previous_sibling()

                    # 提取ul中的所有li元素
                    li_elements = ul.find_all('li')

                    for li in li_elements:
                        # 查找li中的链接
                        link = li.find('a')
                        if link:
                            href = link.get('href', '')
                            text = link.get_text().strip()

                            # 构建完整URL
                            if href:
                                if href.startswith('http'):
                                    full_url = href
                                else:
                                    full_url = urljoin(self.base_url, href)
                            else:
                                full_url = ''

                            recall_item = {
                                '年份': year_header if year_header else '',
                                '产品名称': text,
                                '链接': full_url
                            }
                            recall_items.append(recall_item)
                        else:
                            # 如果li中没有链接，只提取文本
                            text = li.get_text().strip()
                            if text:
                                recall_item = {
                                    '年份': year_header if year_header else '',
                                    '产品名称': text,
                                    '链接': ''
                                }
                                recall_items.append(recall_item)

        return recall_items

    def scrape_recalls(self):
        """主要的抓取方法"""
        # 获取页面内容
        html_content = self.fetch_page(self.target_url)
        if not html_content:
            return []

        # 解析召回产品信息
        recall_items = self.parse_recall_content(html_content)
        return recall_items


def main():
    """主函数"""
    scraper = MPIFoodRecallsScraper()

    # 抓取召回产品信息
    recalls = scraper.scrape_recalls()

    # 输出所有召回产品
    for recall in recalls:
        print(recall)


if __name__ == "__main__":
    main()