import re
import time
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from bs4 import BeautifulSoup
from crawlers.base_crawler import BaseCrawler
from models.data_wanfang_magazine import DataWanfangMagazine

class WfMagazineCrawler(BaseCrawler):
    """
    万方期刊数据抓取爬虫
    """
    def __init__(self, task_id, batch_id=None):
        super().__init__(task_id, batch_id)
        self.driver = self._init_driver()

    def _init_driver(self):
        """
        初始化 Selenium WebDriver
        """
        options = Options()
        options.add_argument('--headless')
        options.add_argument('--disable-gpu')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)
        options.add_argument("user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
        
        driver = webdriver.Chrome(options=options)
        
        driver.execute_cdp_cmd("Page.addScriptToEvaluateOnNewDocument", {
            "source": """
            Object.defineProperty(navigator, 'webdriver', {
              get: () => undefined
            });
            """
        })
        return driver

    def _crawl(self):
        """
        执行抓取逻辑
        """
        try:
            # 初始页面
            base_url = "https://sns.wanfangdata.com.cn/perio/spaqzljcjs"
            self.driver.get(base_url)
            time.sleep(5)
            soup = BeautifulSoup(self.driver.page_source, "lxml")

            # 获取当前期数
            wf_inline = soup.select_one("#chose_year wf-inline")
            if not wf_inline:
                self.logger.error("未找到当前期数元素")
                return False
            
            period_text = wf_inline.get_text(strip=True)
            match = re.search(r"(\d{4})年(\d+)期", period_text)
            if not match:
                self.logger.error(f"未能从'{period_text}'解析出年份和期号")
                return False
                
            publish_year = match.group(1)
            issue_num = match.group(2)
            self.logger.info(f"成功解析到当前期数: {publish_year}年{issue_num}期")

            # 获取总页数
            page_links = soup.select("wf-pagination li a")
            page_nums = [int(re.search(r"page=(\d+)", a.get("href", "")).group(1)) for a in page_links if "page=" in a.get("href", "")]
            total_pages = max(page_nums) if page_nums else 1
            self.logger.info(f"共发现 {total_pages} 页文章")

            all_records = []
            # 遍历每一页
            for page in range(1, total_pages + 1):
                url = f"https://sns.wanfangdata.com.cn/perio/spaqzljcjs/?tabId=article&publishYear={publish_year}&issueNum={issue_num}&isSync=0&page={page}"
                self.logger.info(f"正在抓取第 {page} 页: {url}")
                self.driver.get(url)
                time.sleep(4)
                soup = BeautifulSoup(self.driver.page_source, "lxml")
                article_items = soup.select("wf-article-item")
                
                if not article_items:
                    self.logger.warning(f"第 {page} 页未找到任何文章")
                    continue

                for item in article_items:
                    a_tag = item.find("a", href=True)
                    if not a_tag:
                        continue
                    article_url = a_tag["href"]

                    try:
                        self.driver.get(article_url)
                        time.sleep(3)
                        detail_soup = BeautifulSoup(self.driver.page_source, "lxml")

                        title = detail_soup.select_one("div.detailTitleCN span").get_text(strip=True) if detail_soup.select_one("div.detailTitleCN span") else "无标题"
                        
                        abstract_blocks = detail_soup.select("div.summary.list")
                        abstract = "\n".join(block.get_text(strip=True).replace("摘要：", "", 1) for block in abstract_blocks) if abstract_blocks else "无摘要"
                        
                        publish_date_tag = detail_soup.select_one("div.publish.list div.itemUrl")
                        publish_date = publish_date_tag.get_text(strip=True) if publish_date_tag else "无日期"

                        record = {
                            "title": title,
                            "summary": abstract,
                            "publish_year": publish_year,
                            "issue_num": issue_num,
                            "publish_date": publish_date,
                            "source_url": article_url,
                            "sources": "万方数据库",
                            "country":"中国",
                            "origin_country":"中国",
                        }
                        all_records.append(record)
                        self.logger.info(f"成功抓取文章: {title}")
                        
                    except Exception as e:
                        self.logger.error(f"处理文章详情页 {article_url} 时出错: {e}")
            
            if all_records:
                self.logger.info(f"准备保存 {len(all_records)} 条记录到数据库...")
                self.save_records(all_records, model_class=DataWanfangMagazine)
            else:
                self.logger.info("本次抓取没有发现任何新记录。")

            return True

        except Exception as e:
            self.logger.error(f"抓取过程中发生严重错误: {e}", exc_info=True)
            return False
        finally:
            if self.driver:
                self.driver.quit()
                self.logger.info("WebDriver 已关闭")
