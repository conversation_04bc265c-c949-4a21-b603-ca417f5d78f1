import requests
from bs4 import BeautifulSoup
from datetime import datetime, timedelta
import time
import re

base_url = "https://tag.foodmate.net/1647/standard/"
headers = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) "
                  "AppleWebKit/537.36 (KHTML, like Gecko) "
                  "Chrome/115.0 Safari/537.36"
}

def get_standard_details(detail_url):
    """获取标准详细信息"""
    try:
        print(f"  正在获取详细信息: {detail_url}")
        response = requests.get(detail_url, headers=headers, timeout=10)
        response.encoding = response.apparent_encoding
        soup = BeautifulSoup(response.text, "html.parser")

        # 获取发布日期
        publish_date = None
        tables = soup.find_all("table")
        for table in tables:
            rows = table.find_all("tr")
            for row in rows:
                cells = row.find_all(["th", "td"])
                for i, cell in enumerate(cells):
                    if "发布日期" in cell.get_text(strip=True) and i + 1 < len(cells):
                        publish_date = cells[i + 1].get_text(strip=True)
                        break
                if publish_date:
                    break
            if publish_date:
                break

        # 获取适用范围内容
        content = None
        bznr_box = soup.find("div", class_="bznr_box")
        if bznr_box:
            # 查找适用范围相关的div
            divs = bznr_box.find_all("div")
            for div in divs:
                text = div.get_text(strip=True)
                if "本文件规定了" in text or "适用于" in text:
                    content = text
                    break

        return publish_date, content

    except Exception as e:
        print(f"  获取详细信息失败: {e}")
        return None, None

all_results = []

# 时间范围：今天 ~ 一个月前
today = datetime.today().date()
one_month_ago = today - timedelta(days=30)

page = 1
stop_flag = False

while True:
    if page == 1:
        url = base_url
    else:
        url = f"{base_url}index-{page}.html"

    print(f"正在抓取第 {page} 页: {url}")
    response = requests.get(url, headers=headers, timeout=10)
    response.encoding = response.apparent_encoding
    soup = BeautifulSoup(response.text, "html.parser")

    titles = soup.find_all("li", class_="list_bt")
    dates = soup.find_all("ul", class_="list_wzr")

    for title_li, date_ul in zip(titles, dates):
        # 获取标题和链接
        title_link = title_li.find("a")
        if not title_link:
            continue

        title = title_link.get_text(strip=True)
        detail_url = title_link.get("href")

        # 如果是相对链接，转换为绝对链接
        if detail_url and not detail_url.startswith("http"):
            detail_url = "http://down.foodmate.net" + detail_url

        date_str = date_ul.get_text(strip=True)

        try:
            list_date = datetime.strptime(date_str, "%Y-%m-%d").date()
        except ValueError:
            continue

        if list_date < one_month_ago:
            stop_flag = True
            break  # 当前页已经到一个月前，停止采集
        elif list_date <= today:
            # 获取详细信息
            publish_date, content = get_standard_details(detail_url)

            all_results.append({
                'title': title,
                'detail_url': detail_url,
                'list_date': list_date,
                'publish_date': publish_date,
                'content': content
            })

            time.sleep(0.5)  # 避免请求过快

    if stop_flag:
        break

    page += 1
    time.sleep(1)  # 防止过快请求被封

# 输出结果
print("\n===== 最近1个月内的标准详细信息 =====\n")
for i, result in enumerate(all_results, start=1):
    print(f"{i}. 标题: {result['title']}")
    print(f"   详细链接: {result['detail_url']}")
    print(f"   列表日期: {result['list_date']}")
    print(f"   发布日期: {result['publish_date'] or '未获取到'}")
    print(f"   适用范围: {result['content'] or '未获取到'}")
    print("-" * 80)

print(f"\n共抓取到 {len(all_results)} 条数据（最近1个月）。")
