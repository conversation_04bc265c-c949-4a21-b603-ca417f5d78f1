import requests
from bs4 import BeautifulSoup
from datetime import datetime, timedelta
import time

base_url = "https://tag.foodmate.net/1647/standard/"
headers = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) "
                  "AppleWebKit/537.36 (KHTML, like Gecko) "
                  "Chrome/115.0 Safari/537.36"
}

all_results = []

# 时间范围：今天 ~ 一个月前
today = datetime.today().date()
one_month_ago = today - timedelta(days=30)

page = 1
stop_flag = False

while True:
    if page == 1:
        url = base_url
    else:
        url = f"{base_url}index-{page}.html"

    print(f"正在抓取第 {page} 页: {url}")
    response = requests.get(url, headers=headers, timeout=10)
    response.encoding = response.apparent_encoding
    soup = BeautifulSoup(response.text, "html.parser")

    titles = soup.find_all("li", class_="list_bt")
    dates = soup.find_all("ul", class_="list_wzr")

    for title_li, date_ul in zip(titles, dates):
        title = title_li.get_text(strip=True)
        date_str = date_ul.get_text(strip=True)

        try:
            pub_date = datetime.strptime(date_str, "%Y-%m-%d").date()
        except ValueError:
            continue

        if pub_date < one_month_ago:
            stop_flag = True
            break  # 当前页已经到一个月前，停止采集
        elif pub_date <= today:
            all_results.append((title, pub_date))

    if stop_flag:
        break

    page += 1
    time.sleep(1)  # 防止过快请求被封

# 输出结果
print("\n===== 最近1个月内的标准 =====\n")
for i, (title, pub_date) in enumerate(all_results, start=1):
    print(f"{i}. {pub_date} | {title}")

print(f"\n共抓取到 {len(all_results)} 条数据（最近1个月）。")
