#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ScienceDirect期刊和图书数据抓取脚本
使用Selenium抓取https://www.sciencedirect.com/browse/journals-and-books页面的出版物数据
"""

import re
import time
import json
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from bs4 import BeautifulSoup


class ScienceDirectSeleniumScraper:
    def __init__(self):
        self.base_url = "https://www.sciencedirect.com/browse/journals-and-books?searchPhrase=chemosphere"
        self.driver = None
        self.setup_driver()

    def setup_driver(self):
        options = Options()
        # options.add_argument('--headless')
        options.add_argument('--disable-gpu')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)
        options.add_argument("user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")

        self.driver = webdriver.Chrome(options=options)

        self.driver.execute_cdp_cmd("Page.addScriptToEvaluateOnNewDocument", {
            "source": """
            Object.defineProperty(navigator, 'webdriver', {
              get: () => undefined
            });
            """
        })

    def extract_publication_data(self, li_element):
        try:
            title_link = li_element.find('a', class_='anchor js-publication-title anchor-primary')
            if not title_link:
                return None

            href = title_link.get('href', '')
            title_span = title_link.find('span', class_='anchor-text')
            display_name = title_span.get_text(strip=True) if title_span else ''

            title_slug = ''
            isbn = ''
            if href:
                path_parts = href.strip('/').split('/')
                if len(path_parts) >= 2:
                    title_slug = path_parts[-1]
                    if 'book' in href and len(path_parts) >= 3:
                        potential_isbn = path_parts[-2]
                        if potential_isbn.isdigit() and len(potential_isbn) >= 10:
                            isbn = potential_isbn

            subtitle_p = li_element.find('p', class_='publication-text')
            subtitle = subtitle_p.get_text(strip=True) if subtitle_p else ''

            content_type = ''
            publication_year = ''

            content_div = li_element.find('div', class_='publication-text')
            if content_div:
                type_p = content_div.find('p', class_='js-publication-content-type')
                year_p = content_div.find('p', class_='js-publication-year')

                if type_p:
                    content_type = type_p.get_text(strip=True)
                if year_p:
                    year_text = year_p.get_text(strip=True)
                    year_match = re.search(r'\d{4}', year_text)
                    if year_match:
                        publication_year = year_match.group()

            sort_name = re.sub(r'[^\w\s]', '', display_name).strip()
            sort_name = ' '.join(sort_name.split())

            content_type_code = 'BK' if 'book' in content_type.lower() else 'JL'

            return {
                'titleSlug': title_slug,
                'sortName': sort_name,
                'displayName': display_name,
                'isbn': isbn,
                'publicationYear': publication_year,
                'contentTypeCode': content_type_code
            }

        except Exception as e:
            print(f"提取数据时出错: {e}")
            return None

    def scrape_page(self, url, max_retries=3):
        for attempt in range(max_retries):
            try:
                self.driver.get(url)
                time.sleep(3)

                # 尝试等待出版物元素，但如果没有也不报错
                try:
                    WebDriverWait(self.driver, 10).until(
                        EC.presence_of_element_located((By.CLASS_NAME, "js-publication"))
                    )
                except:
                    # 如果没有找到js-publication元素，继续尝试解析页面
                    pass

                soup = BeautifulSoup(self.driver.page_source, 'html.parser')
                publication_items = soup.find_all('li', class_='publication u-padding-xs-ver js-publication')

                publications = []
                for item in publication_items:
                    pub_data = self.extract_publication_data(item)
                    if pub_data:
                        publications.append(pub_data)

                # 如果没有找到数据，检查页面是否正常加载
                if len(publications) == 0:
                    # 检查页面是否有错误信息或者确实没有数据
                    page_text = soup.get_text().lower()
                    if 'no results' in page_text or 'not found' in page_text or '0 results' in page_text:
                        print("页面显示无结果")
                        return []

                return publications

            except Exception as e:
                print(f"抓取页面时出错 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    print(f"等待5秒后重试...")
                    time.sleep(5)
                else:
                    print(f"页面抓取失败，跳过: {url}")
                    return []

    def get_total_pages(self):
        try:
            self.driver.get(self.base_url)
            time.sleep(3)

            soup = BeautifulSoup(self.driver.page_source, 'html.parser')

            # 尝试多种分页元素查找方式
            pagination_label = None

            # 方式1: 查找 pagination-pages-label
            pagination_label = soup.find('span', class_='pagination-pages-label')

            # 方式2: 如果没找到，尝试其他分页相关的元素
            if not pagination_label:
                pagination_label = soup.find('span', string=re.compile(r'Page \d+ of \d+'))

            # 方式3: 查找分页导航中的最后一页链接
            if not pagination_label:
                pagination_links = soup.find_all('a', href=re.compile(r'page=\d+'))
                if pagination_links:
                    page_numbers = []
                    for link in pagination_links:
                        href = link.get('href', '')
                        match = re.search(r'page=(\d+)', href)
                        if match:
                            page_numbers.append(int(match.group(1)))
                    if page_numbers:
                        total_pages = max(page_numbers)
                        print(f"从分页链接检测到总页数: {total_pages}")
                        return total_pages

            # 如果找到了分页标签，解析页数
            if pagination_label:
                text = pagination_label.get_text(strip=True)
                match = re.search(r'Page \d+ of (\d+)', text)
                if match:
                    total_pages = int(match.group(1))
                    print(f"检测到总页数: {total_pages}")
                    return total_pages

            # 如果都没找到，检查是否有数据
            publication_items = soup.find_all('li', class_='publication u-padding-xs-ver js-publication')
            if publication_items:
                print(f"未找到分页信息，但检测到 {len(publication_items)} 条数据，默认为1页")
            else:
                print("未找到分页信息和数据，默认为1页")

            return 1

        except Exception as e:
            print(f"获取总页数时出错: {e}")
            return 1

    def scrape_all_publications(self, max_pages=None):
        all_publications = []

        try:
            total_pages = self.get_total_pages()

            if max_pages:
                total_pages = min(total_pages, max_pages)
                print(f"限制抓取页数为: {total_pages}")

            for page in range(1, total_pages + 1):
                # 构建页面URL，考虑已有参数
                if '?' in self.base_url:
                    page_url = f"{self.base_url}&page={page}"
                else:
                    page_url = f"{self.base_url}?page={page}"

                print(f"\n正在抓取第 {page}/{total_pages} 页...")
                publications = self.scrape_page(page_url)
                all_publications.extend(publications)

                print(f"第{page}页抓取到 {len(publications)} 条记录，累计 {len(all_publications)} 条")

                # 如果当前页没有数据且不是第一页，可能已经到达末尾
                if len(publications) == 0 and page > 1:
                    print("当前页无数据，可能已到达末尾，停止抓取")
                    break

                if total_pages > 1:
                    progress = (page / total_pages) * 100
                    print(f"进度: {progress:.1f}%")

                if page < total_pages:
                    time.sleep(2)

        except KeyboardInterrupt:
            print(f"\n用户中断，已抓取 {len(all_publications)} 条记录")
        except Exception as e:
            print(f"抓取过程中出错: {e}")

        return all_publications

    def close(self):
        if self.driver:
            self.driver.quit()


def main():

    MAX_PAGES = 2  # 设置为None抓取所有页面，或设置数字限制页数

    if MAX_PAGES:
        print(f"配置: 限制抓取前 {MAX_PAGES} 页")
    else:
        print("配置: 抓取所有页面")
        print("注意: 完整抓取可能需要数小时，建议先设置MAX_PAGES测试")

    print("-" * 50)

    scraper = ScienceDirectSeleniumScraper()

    try:
        publications = scraper.scrape_all_publications(max_pages=MAX_PAGES)

        print(f"\n" + "=" * 50)
        print(f"抓取完成！总共获取到 {len(publications)} 条记录")

        for publication in publications:
            print(json.dumps(publication, ensure_ascii=False))

    finally:
        scraper.close()


if __name__ == "__main__":
    main()