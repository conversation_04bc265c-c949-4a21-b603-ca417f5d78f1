#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
香港食物安全中心网站抓取脚本
抓取https://www.cfs.gov.hk/sc_chi/whatsnew/whatsnew_fstr/whatsnew_fstr.html页面的内容
"""

import requests
from bs4 import BeautifulSoup
import time
from urllib.parse import urljoin
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


class CFSHKScraper:
    def __init__(self):
        self.base_url = "https://www.cfs.gov.hk"
        self.target_url = "https://www.cfs.gov.hk/sc_chi/whatsnew/whatsnew_fstr/whatsnew_fstr.html"
        self.session = requests.Session()

        # 设置请求头，模拟浏览器访问
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })

    def fetch_page(self, url):
        """获取网页内容"""
        try:
            # 禁用SSL证书验证来解决证书问题
            response = self.session.get(url, timeout=30, verify=False)
            response.raise_for_status()
            response.encoding = 'utf-8'
            return response.text
        except requests.RequestException as e:
            print(f"请求失败: {e}")
            return None

    def parse_news_content(self, html_content):
        """解析新闻内容"""
        soup = BeautifulSoup(html_content, 'html.parser')
        news_items = []

        # 查找所有tr元素
        table_rows = soup.find_all('tr')

        for tr in table_rows:
            # 查找包含日期的td.subHeader
            date_td = tr.find('td', class_='subHeader')
            if date_td:
                # 提取日期文本，去掉箭头符号
                date_text = date_td.get_text().strip()
                # 移除箭头符号和其他特殊字符
                date_text = date_text.replace('▼', '').replace('▲', '').replace('►', '').replace('◄', '').replace('&nbsp;', '').strip()

                # 查找同一行中的其他td，包含ul和li
                content_td = tr.find('td', class_=lambda x: x != 'subHeader')
                if content_td:
                    # 查找所有li元素
                    li_elements = content_td.find_all('li')

                    if li_elements:
                        # 提取所有li的内容
                        li_contents = []
                        for li in li_elements:
                            # 获取li的文本内容
                            li_text = li.get_text().strip()
                            # 如果li中有链接，也提取链接信息
                            link = li.find('a')
                            if link:
                                href = link.get('href', '')
                                if href:
                                    full_url = urljoin(self.base_url, href)
                                    li_text += f" [链接: {full_url}]"
                            li_contents.append(li_text)

                        # 用逗号分隔多个li内容
                        content = ', '.join(li_contents)

                        news_item = {
                            '日期': date_text,
                            '内容': content
                        }
                        news_items.append(news_item)

        return news_items

    def scrape_news(self):
        """主要的抓取方法"""
        # 获取页面内容
        html_content = self.fetch_page(self.target_url)
        if not html_content:
            return []

        # 解析新闻内容
        news_items = self.parse_news_content(html_content)
        return news_items


def main():
    """主函数"""
    scraper = CFSHKScraper()

    # 抓取新闻信息
    news_items = scraper.scrape_news()

    # 输出所有新闻项目
    for item in news_items:
        print(item)


if __name__ == "__main__":
    main()