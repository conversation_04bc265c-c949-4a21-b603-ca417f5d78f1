import re
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from bs4 import BeautifulSoup
import time

# 设置 headless Chrome
options = Options()
options.add_argument('--headless')
options.add_argument('--disable-gpu')
options.add_argument('--no-sandbox')
options.add_argument('--disable-dev-shm-usage')
options.add_argument('--disable-blink-features=AutomationControlled')
options.add_experimental_option("excludeSwitches", ["enable-automation"])
options.add_experimental_option('useAutomationExtension', False)
options.add_argument("user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")

driver = webdriver.Chrome(options=options)

# 反反爬设置
driver.execute_cdp_cmd("Page.addScriptToEvaluateOnNewDocument", {
    "source": """
    Object.defineProperty(navigator, 'webdriver', {
      get: () => undefined
    });
    """
})

# 初始页面
base_url = "https://sns.wanfangdata.com.cn/perio/spaqzljcjs"
driver.get(base_url)
time.sleep(5)
soup = BeautifulSoup(driver.page_source, "lxml")

# ✅ 获取当前期数，如“2025年12期”
wf_inline = soup.select_one("#chose_year wf-inline")
if wf_inline:
    period_text = wf_inline.get_text(strip=True)
    match = re.search(r"(\d{4})年(\d+)期", period_text)
    if match:
        publish_year = match.group(1)
        issue_num = match.group(2)
    else:
        raise ValueError("未能解析出年份和期号")
else:
    raise ValueError("未找到当前期数")

# ✅ 获取总页数
page_links = soup.select("wf-pagination li a")
page_nums = []
for a in page_links:
    href = a.get("href", "")
    if "page=" in href:
        try:
            page_num = int(href.split("page=")[-1])
            page_nums.append(page_num)
        except:
            pass
total_pages = max(page_nums) if page_nums else 1

# ✅ 遍历每一页
for page in range(1, total_pages + 1):
    url = f"https://sns.wanfangdata.com.cn/perio/spaqzljcjs/?tabId=article&publishYear={publish_year}&issueNum={issue_num}&isSync=0&page={page}"
    print(f"\n正在抓取第 {page} 页: {url}")
    driver.get(url)
    time.sleep(4)
    soup = BeautifulSoup(driver.page_source, "lxml")
    article_items = soup.select("wf-article-item")

    for item in article_items:
        # 提取文章链接
        a_tag = item.find("a", href=True)
        if not a_tag:
            continue
        article_url = a_tag["href"]

        # 请求文章详情页
        driver.get(article_url)
        time.sleep(3)
        detail_soup = BeautifulSoup(driver.page_source, "lxml")

        # 标题
        title_tag = detail_soup.select_one("div.detailTitleCN span")
        title = title_tag.get_text(strip=True) if title_tag else "无标题"

        # 摘要
        abstract_blocks = detail_soup.select("div.summary.list")
        if abstract_blocks:
            all_abstracts = []
            for block in abstract_blocks:
                text = block.get_text(strip=True)
                if text.startswith("摘要："):
                    text = text[len("摘要："):]  # 去掉“摘要：”
                all_abstracts.append(text)
            abstract = "\n".join(all_abstracts)
        else:
            abstract = "无摘要"

        # 发表日期
        date_tag = detail_soup.select_one("div.publish.list span.item")
        date_value = detail_soup.select_one("div.publish.list div.itemUrl")
        if date_tag and "论文发表日期" in date_tag.get_text():
            publish_date = date_value.get_text(strip=True) if date_value else "无日期"
        else:
            publish_date = "无日期"

        # 打印格式化信息
        print("信息来源：万方数据库")
        print(f"任务标题：{title}")
        print(f"任务摘要：{abstract}")
        print(f"发布日期：{publish_date}")
        print(f"原始链接：{article_url}")
        print("通报国别：中国")
        print("产地国别：中国")
        print("=" * 60)

driver.quit()
