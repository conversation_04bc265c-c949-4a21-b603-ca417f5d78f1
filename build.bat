@echo off
chcp 65001 > nul
echo 正在打包 app.exe...

REM 设置为发布模式（DEBUG=false）
set DEBUG=false

REM 打包主程序
pyinstaller -F -w app.py ^
--add-data "templates;templates" ^
--add-data "static;static" ^
--add-data "routes;routes" ^
--add-data "services;services" ^
--add-data "models;models" ^
--add-data "utils;utils" ^
--add-data "crawlers;crawlers" ^
--add-data "config.py;."

REM 自动复制 chromedriver.exe 到 dist 目录（如果你希望自动做）
if exist chromedriver.exe (
    copy /Y chromedriver.exe dist\
    echo ✅ chromedriver.exe 已复制到 dist\
) else (
    echo ⚠️ 请手动将 chromedriver.exe 复制到 dist\
)

echo ✅ 打包完成！可执行文件位于 dist\app.exe
pause
