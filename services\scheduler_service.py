import logging
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.jobstores.memory import MemoryJobStore
from apscheduler.executors.pool import ThreadPoolExecutor
from apscheduler.triggers.cron import CronTrigger
from config import Config
from models.task_config import TaskConfig
from services.capture_service import CaptureService

class SchedulerService:
    """
    调度器服务，用于管理定时任务
    """
    
    _instance = None
    
    @classmethod
    def get_instance(cls):
        """
        获取单例实例
        
        Returns:
            SchedulerService: 调度器服务实例
        """
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance
    
    def __init__(self):
        """
        初始化调度器服务
        """
        self.logger = logging.getLogger("service.scheduler")
        
        # 配置调度器
        jobstores = {
            'default': MemoryJobStore()
        }
        executors = {
            'default': ThreadPoolExecutor(20)
        }
        job_defaults = {
            'coalesce': Config.JOBS_COALESCE,
            'misfire_grace_time': Config.JOBS_MISFIRE_GRACE_TIME
        }
        
        self.scheduler = BackgroundScheduler(
            jobstores=jobstores,
            executors=executors,
            job_defaults=job_defaults,
            timezone=Config.SCHEDULER_TIMEZONE
        )
        
        self.capture_service = CaptureService()
        self._is_running = False
    
    def start(self):
        """
        启动调度器
        """
        if not self._is_running:
            self.scheduler.start()
            self._is_running = True
            self.logger.info("调度器已启动")
            
            # 加载所有启用的任务
            self.reload_tasks()
    
    def stop(self):
        """
        停止调度器
        """
        if self._is_running:
            self.scheduler.shutdown()
            self._is_running = False
            self.logger.info("调度器已停止")
    
    def reload_tasks(self):
        """
        重新加载所有任务
        """
        # 先移除所有任务
        self.scheduler.remove_all_jobs()
        
        # 加载所有启用的任务
        tasks = TaskConfig.get_all(include_disabled=False)
        for task in tasks:
            self.add_task(task)
            
        self.logger.info(f"已加载 {len(tasks)} 个任务")
    
    def add_task(self, task):
        """
        添加任务
        
        Args:
            task (TaskConfig): 任务配置
            
        Returns:
            bool: 添加成功返回True，否则返回False
        """
        try:
            if not task.is_enabled:
                return False
                
            job_id = f"task_{task.id}"
            
            # 检查任务是否已存在
            if self.scheduler.get_job(job_id):
                self.scheduler.remove_job(job_id)
                
            # 添加任务
            self.scheduler.add_job(
                self.capture_service.execute_task,
                CronTrigger.from_crontab(task.schedule_cron),
                id=job_id,
                name=task.task_name,
                args=[task.id],
                replace_existing=True
            )
            
            self.logger.info(f"已添加任务: {task.task_name} (ID: {task.id}), 调度: {task.schedule_cron}")
            return True
        except Exception as e:
            self.logger.error(f"添加任务失败: {e}")
            return False
    
    def remove_task(self, task_id):
        """
        移除任务
        
        Args:
            task_id (int): 任务ID
            
        Returns:
            bool: 移除成功返回True，否则返回False
        """
        try:
            job_id = f"task_{task_id}"
            
            # 检查任务是否存在
            if self.scheduler.get_job(job_id):
                self.scheduler.remove_job(job_id)
                self.logger.info(f"已移除任务: {task_id}")
                return True
            return False
        except Exception as e:
            self.logger.error(f"移除任务失败: {e}")
            return False
    
    def update_task(self, task):
        """
        更新任务
        
        Args:
            task (TaskConfig): 任务配置
            
        Returns:
            bool: 更新成功返回True，否则返回False
        """
        try:
            job_id = f"task_{task.id}"
            
            # 检查任务是否存在
            if self.scheduler.get_job(job_id):
                self.scheduler.remove_job(job_id)
                
            # 如果任务已禁用，则不添加
            if not task.is_enabled:
                self.logger.info(f"任务已禁用: {task.task_name} (ID: {task.id})")
                return True
                
            # 添加任务
            self.scheduler.add_job(
                self.capture_service.execute_task,
                CronTrigger.from_crontab(task.schedule_cron),
                id=job_id,
                name=task.task_name,
                args=[task.id],
                replace_existing=True
            )
            
            self.logger.info(f"已更新任务: {task.task_name} (ID: {task.id}), 调度: {task.schedule_cron}")
            return True
        except Exception as e:
            self.logger.error(f"更新任务失败: {e}")
            return False
    
    def get_task_status(self, task_id):
        """
        获取任务状态
        
        Args:
            task_id (int): 任务ID
            
        Returns:
            dict: 任务状态信息，如果任务不存在则返回None
        """
        try:
            job_id = f"task_{task_id}"
            job = self.scheduler.get_job(job_id)
            
            if job:
                return {
                    "job_id": job.id,
                    "name": job.name,
                    "next_run_time": job.next_run_time,
                    "trigger": str(job.trigger)
                }
            return None
        except Exception as e:
            self.logger.error(f"获取任务状态失败: {e}")
            return None
    
    def get_all_jobs(self):
        """
        获取所有任务
        
        Returns:
            list: 任务列表
        """
        jobs = []
        for job in self.scheduler.get_jobs():
            jobs.append({
                "job_id": job.id,
                "name": job.name,
                "next_run_time": job.next_run_time,
                "trigger": str(job.trigger)
            })
        return jobs
    
    def run_task_now(self, task_id):
        """
        立即运行任务
        
        Args:
            task_id (int): 任务ID
            
        Returns:
            bool: 运行成功返回True，否则返回False
        """
        try:
            # 检查任务是否存在
            task = TaskConfig.get_by_id(task_id)
            if not task:
                self.logger.error(f"任务不存在: {task_id}")
                return False
                
            # 执行任务
            self.capture_service.execute_task(task_id)
            return True
        except Exception as e:
            self.logger.error(f"立即运行任务失败: {e}")
            return False 