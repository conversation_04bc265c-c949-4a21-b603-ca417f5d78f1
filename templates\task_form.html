{% extends 'base.html' %}

{% block title %}{{ '编辑' if task and task.id else '添加' }}任务 - 数据抓取管理平台{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>{{ '编辑' if task and task.id else '添加' }}任务</h2>
    <a href="{{ url_for('tasks.task_list') }}" class="btn btn-outline-secondary">返回任务列表</a>
</div>

<div class="card">
    <div class="card-body">
        <form method="post" action="{{ url_for('tasks.task_form', task_id=task.id) if task else url_for('tasks.task_form') }}">
            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
            
            <div class="form-group">
                <label for="crawler_type">爬虫类型 <span class="text-danger">*</span></label>
                <select class="form-control" id="crawler_type">
                    <option value="">-- 请选择爬虫类型 --</option>
                    {% for crawler in available_crawlers %}
                    <option value="{{ crawler }}">{{ crawler }}</option>
                    {% endfor %}
                </select>
                <small class="form-text text-muted">选择要使用的爬虫类型</small>
            </div>
            
            <div class="form-group">
                <label for="task_name">任务名称 <span class="text-danger">*</span></label>
                <select class="form-control" id="task_name" name="task_name" required>
                    <option value="">-- 请先选择爬虫类型 --</option>
                    {% if task and task.id %}
                    <option value="{{ task.task_name }}" selected>{{ task.task_name }}</option>
                    {% endif %}
                </select>
                <small class="form-text text-muted">选择预设的任务名称</small>
            </div>
            
            <div class="form-group">
                <label for="schedule_cron">Cron表达式 <span class="text-danger">*</span></label>
                <input type="text" class="form-control" id="schedule_cron" name="schedule_cron" value="{{ task.schedule_cron if task else '' }}" required>
                <small class="form-text text-muted">例如：0 0 * * * (每天午夜执行)</small>
            </div>
            
            <div class="form-group">
                <div class="custom-control custom-checkbox">
                    <input type="checkbox" class="custom-control-input" id="is_enabled" name="is_enabled" value="1" {% if not task or task.is_enabled == 1 %}checked{% endif %}>
                    <label class="custom-control-label" for="is_enabled">启用任务</label>
                </div>
            </div>
            
            <div class="form-group">
                <button type="submit" class="btn btn-primary">保存</button>
                <a href="{{ url_for('tasks.task_list') }}" class="btn btn-secondary ml-2">取消</a>
            </div>
        </form>
    </div>
</div>

<div class="card mt-4">
    <div class="card-header">
        <h5 class="mb-0">Cron表达式帮助</h5>
    </div>
    <div class="card-body">
        <p>Cron表达式由5个或6个字段组成，格式为：<code>分 时 日 月 周 [年]</code></p>
        <table class="table table-sm">
            <thead>
                <tr>
                    <th>字段</th>
                    <th>允许值</th>
                    <th>特殊字符</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>分钟</td>
                    <td>0-59</td>
                    <td><code>* , - /</code></td>
                </tr>
                <tr>
                    <td>小时</td>
                    <td>0-23</td>
                    <td><code>* , - /</code></td>
                </tr>
                <tr>
                    <td>日期</td>
                    <td>1-31</td>
                    <td><code>* , - / ? L W</code></td>
                </tr>
                <tr>
                    <td>月份</td>
                    <td>1-12</td>
                    <td><code>* , - /</code></td>
                </tr>
                <tr>
                    <td>星期</td>
                    <td>0-6 或 SUN-SAT</td>
                    <td><code>* , - / ? L #</code></td>
                </tr>
            </tbody>
        </table>
        <p>常用示例：</p>
        <ul>
            <li><code>0 0 * * *</code> - 每天午夜执行</li>
            <li><code>0 */2 * * *</code> - 每2小时执行一次</li>
            <li><code>0 9 * * 1-5</code> - 每个工作日上午9点执行</li>
            <li><code>0 0 1 * *</code> - 每月1日午夜执行</li>
        </ul>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // 任务模板数据
    var taskTemplates = {{ task_templates|tojson }};
    
    // 爬虫类型变更时更新任务名称下拉框
    document.getElementById('crawler_type').addEventListener('change', function() {
        var crawlerType = this.value;
        var taskNameSelect = document.getElementById('task_name');
        
        // 清空现有选项
        taskNameSelect.innerHTML = '';
        
        // 添加默认选项
        var defaultOption = document.createElement('option');
        defaultOption.value = '';
        defaultOption.textContent = crawlerType ? '-- 请选择任务名称 --' : '-- 请先选择爬虫类型 --';
        taskNameSelect.appendChild(defaultOption);
        
        // 如果选择了爬虫类型，添加相应的任务模板
        if (crawlerType && taskTemplates[crawlerType]) {
            taskTemplates[crawlerType].forEach(function(template) {
                var option = document.createElement('option');
                option.value = template;
                option.textContent = template;
                taskNameSelect.appendChild(option);
            });
        }
    });
    
    // 表单提交前验证
    document.querySelector('form').addEventListener('submit', function(e) {
        var cronExpr = document.getElementById('schedule_cron').value.trim();
        
        // 简单的Cron表达式验证
        var cronPattern = /^(\S+\s+){4}\S+$/;
        if (!cronPattern.test(cronExpr)) {
            e.preventDefault();
            alert('Cron表达式格式不正确，请检查！');
            return;
        }
    });
    
    // 如果是编辑模式，尝试根据任务名称自动选择爬虫类型
    {% if task and task.id %}
    (function() {
        var taskName = "{{ task.task_name }}";
        var crawlerTypeSelect = document.getElementById('crawler_type');
        
        // 遍历所有爬虫类型，查找匹配的任务模板
        for (var crawlerType in taskTemplates) {
            if (taskTemplates[crawlerType].indexOf(taskName) !== -1) {
                crawlerTypeSelect.value = crawlerType;
                crawlerTypeSelect.dispatchEvent(new Event('change'));
                document.getElementById('task_name').value = taskName;
                break;
            }
        }
    })();
    {% endif %}
</script>
{% endblock %} 