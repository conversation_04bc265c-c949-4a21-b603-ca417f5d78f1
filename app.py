import os
import logging
from datetime import datetime
from flask import Flask, render_template
from flask_wtf.csrf import CSRFProtect
from config import Config
from routes.index import index_bp
from routes.tasks import tasks_bp
from routes.batches import batches_bp
from services.scheduler_service import SchedulerService

import sys

# 判断运行路径（是否为打包后的 .exe）
if getattr(sys, 'frozen', False):
    base_path = os.path.dirname(sys.executable)  # exe 所在目录
else:
    base_path = os.path.dirname(os.path.abspath(__file__))

# 配置日志
log_level = getattr(logging, Config.LOG_LEVEL)
log_format = Config.LOG_FORMAT
log_date_format = Config.LOG_DATE_FORMAT

log_handlers = []

# 控制台日志
console_handler = logging.StreamHandler()
log_handlers.append(console_handler)

# 发布模式下写入日志文件
if not Config.DEBUG:
    log_dir = os.path.join(base_path, 'log')
    os.makedirs(log_dir, exist_ok=True)
    log_filename = datetime.now().strftime('%Y-%m-%d') + '.log'
    log_path = os.path.join(log_dir, log_filename)
    file_handler = logging.FileHandler(log_path, encoding='utf-8')
    log_handlers.append(file_handler)

logging.basicConfig(
    level=log_level,
    format=log_format,
    datefmt=log_date_format,
    handlers=log_handlers
)


# 创建应用
app = Flask(__name__)
app.config.from_object(Config)
app.secret_key = Config.SECRET_KEY

# 启用CSRF保护
csrf = CSRFProtect(app)

# 注册蓝图
app.register_blueprint(index_bp)
app.register_blueprint(tasks_bp)
app.register_blueprint(batches_bp)

# 创建服务实例
scheduler_service = SchedulerService.get_instance()

@app.template_filter('datetime')
def format_datetime(value, format='%Y-%m-%d %H:%M:%S'):
    """格式化日期时间"""
    if value is None:
        return ""
    if isinstance(value, str):
        try:
            value = datetime.strptime(value, '%Y-%m-%d %H:%M:%S')
        except ValueError:
            return value
    return value.strftime(format)

@app.template_filter('truncate')
def truncate_text(text, length=50, suffix='...'):
    """截断文本"""
    if text is None:
        return ""
    if len(text) <= length:
        return text
    else:
        return text[:length] + suffix

@app.errorhandler(404)
def page_not_found(e):
    """404页面"""
    return render_template('404.html'), 404

@app.errorhandler(500)
def internal_server_error(e):
    """500页面"""
    return render_template('500.html'), 500

# 启动应用
if __name__ == '__main__':
    # 启动调度器
    scheduler_service.start()
    
    # 启动Flask应用
    app.run(debug=Config.DEBUG, host='0.0.0.0', port=5000)
