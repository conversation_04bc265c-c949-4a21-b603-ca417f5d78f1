import requests
import certifi
from datetime import datetime, timedelta
import re
import json
from bs4 import BeautifulSoup
from utils.tencent_translate import translate_text
from utils.project_utils import Utils

# 列表页请求头
LIST_HEADERS = {
    "User-Agent": "Apifox/1.0.0 (https://apifox.com)",
    "Accept": "*/*",
    "Referer": (
        "http://www.fda.gov/datatables/views/ajax?"
        "start=0&length=10&view_base_path=safety/recalls-"
        "market-withdrawals-safety-alerts/datatables-data&"
        "view_display_id=recall_datatable_block_1&"
        "view_dom_id=468f2a989f41f60305df7d10dce852e147d601cad167cf89728e4b38b5646899&"
        "view_name=recall_solr_index"
    ),
}

# 详情页请求头
DETAIL_HEADERS = {
    "User-Agent": LIST_HEADERS["User-Agent"],
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
    "Referer": LIST_HEADERS["Referer"]
}

def parse_recall_detail_en(session, url):
    """
    解析单条 recall 详情页，返回所需字段字典。
    """
    try:
        r = session.get(url, headers=DETAIL_HEADERS, allow_redirects=False)
        # 跳过反爬页面
        if r.status_code in (301, 302) and "abuse-detection-apology" in r.headers.get("Location", ""):
            return {}
        r.raise_for_status()
    except requests.RequestException:
        return {}

    soup = BeautifulSoup(r.text, "html.parser")

    # 信息来源、固定字段
    info = {
        "信息来源":    "美国食品和药物管理局（US FDA）",
        "原始链接":    url,
        "通报国别":    "美国",
        "产地国别":    "美国"
    }

    # 任务标题
    h1 = soup.select_one("h1.content-title")
    original_title = h1.get_text(strip=True) if h1 else ""
    info["任务标题"] = translate_text(original_title)
    # 任务摘要：找到 Company Announcement 下第一个含 <strong> 的 <p>
    summary = ""
    announce_h2 = soup.select_one("h2#recall-announcement")
    if announce_h2:
        for sib in announce_h2.find_next_siblings():
            if sib.name == "p" or sib.find("strong"):
                summary = sib.get_text(strip=True)
                break
    info["任务摘要"] = translate_text(summary)

    # 提取 <dl> 中 dt/dd
    data = {}
    dl = soup.select_one("dl.lcds-description-list--grid")
    if dl:
        for dt, dd in zip(dl.find_all("dt"), dl.find_all("dd")):
            label = dt.get_text(" ", strip=True).rstrip(":")
            value = dd.get_text(" ", strip=True)
            data[label] = value

    # 映射到目标字段
    info["发布日期"]   = Utils.format_date_string(data.get("FDA Publish Date", ""), "%B %d, %Y", "%Y年%m月%d日")
    info["食品类别"]   = translate_text(data.get("Product Type", ""))
    info["风险因子"]   = translate_text(data.get("Product Name", ""))
    info["产品名称"]   = translate_text(data.get("Product Description", ""))
    info["生产企业"]   = translate_text(data.get("Company Name", ""))

    return info

def fetch_filtered_recalls(page_size=50):
    """
    抓取近 30 天内 recalls，排除指定分类，返回详情字段列表。
    """
    today = datetime.utcnow().date()
    start_date = today - timedelta(days=30)
    excluded = {"Drugs", "Medical Devices", "Animal & Veterinary"}

    session = requests.Session()
    session.verify = certifi.where()

    api_url = "https://www.fda.gov/datatables/views/ajax"
    params = {
        "start": 0,
        "length": page_size,
        "view_base_path": "safety/recalls-market-withdrawals-safety-alerts/datatables-data",
        "view_display_id": "recall_datatable_block_1",
        "view_dom_id": "468f2a989f41f60305df7d10dce852e147d601cad167cf89728e4b38b5646899",
        "view_name": "recall_solr_index"
    }

    results = []
    while True:
        resp = session.get(api_url, headers=LIST_HEADERS, params=params)
        resp.raise_for_status()
        rows = resp.json().get("data", [])
        if not rows:
            break

        stop = False
        for row in rows:
            # 列表页日期
            m = re.search(r'datetime="(\d{4}-\d{2}-\d{2})', row[0])
            if not m:
                continue
            date = datetime.strptime(m.group(1), "%Y-%m-%d").date()
            if date < start_date:
                stop = True
                break

            # 过滤分类
            cat = row[3].strip()
            if date < start_date or date > today or cat in excluded:
                continue

            # 提取详情链接
            link = re.search(r'<a href="([^"]+)">', row[1])
            if not link:
                continue
            path = link.group(1)
            if not path.startswith("/safety/recalls-market-withdrawals-safety-alerts/"):
                continue
            url = f"https://www.fda.gov{path}"

            # 解析详情并添加
            detail = parse_recall_detail_en(session, url)
            if detail:
                results.append(detail)

        if stop:
            break
        params["start"] += page_size

    return results

if __name__ == "__main__":
    records = fetch_filtered_recalls()
    print(f"共抓取到 {len(records)} 条记录\n")
    print(json.dumps(records, indent=4, ensure_ascii=False))
