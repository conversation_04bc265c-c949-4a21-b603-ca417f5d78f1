-- 定时任务配置表
CREATE TABLE capture_task_config (
    id              NUMBER(10)      PRIMARY KEY,
    task_name       VARCHAR2(100)   NOT NULL,
    schedule_cron   VARCHAR2(50)    NOT NULL, -- cron表达式
    is_enabled      NUMBER(1)       DEFAULT 1, -- 1=启用, 0=禁用
    created_at      DATE            DEFAULT SYSDATE,
    updated_at      DATE
);

COMMENT ON COLUMN capture_task_config.id IS '主键，任务配置唯一ID';
COMMENT ON COLUMN capture_task_config.task_name IS '定时任务名称';
COMMENT ON COLUMN capture_task_config.schedule_cron IS '定时任务cron表达式';
COMMENT ON COLUMN capture_task_config.is_enabled IS '任务是否启用（1=启用，0=禁用）';
COMMENT ON COLUMN capture_task_config.created_at IS '记录创建时间';
COMMENT ON COLUMN capture_task_config.updated_at IS '记录最后更新时间';

-- 抓取批次表
CREATE TABLE capture_data_batch (
    batch_id        VARCHAR2(50)    PRIMARY KEY,
    task_id         NUMBER(10)      NOT NULL,
    capture_time    DATE            NOT NULL,
    record_count    NUMBER(10),
    status          VARCHAR2(20), -- SUCCESS/FAILED
    remark          VARCHAR2(200)
);

COMMENT ON COLUMN capture_data_batch.batch_id IS '主键，批次唯一ID';
COMMENT ON COLUMN capture_data_batch.task_id IS '关联任务配置ID';
COMMENT ON COLUMN capture_data_batch.capture_time IS '本批次抓取时间';
COMMENT ON COLUMN capture_data_batch.record_count IS '本批次抓取数据条数';
COMMENT ON COLUMN capture_data_batch.status IS '批次状态（如SUCCESS、FAILED）';
COMMENT ON COLUMN capture_data_batch.remark IS '备注信息';

-- FDA数据表
CREATE TABLE capture_data_fda (
    id              NUMBER(20)      PRIMARY KEY,
    batch_id        VARCHAR2(50)    NOT NULL,
    task_id         NUMBER(10)      NOT NULL,
    title           VARCHAR2(500),
    summary         VARCHAR2(2000),
    publish_date    VARCHAR2(50),
    category        VARCHAR2(200),
    risk_factor     VARCHAR2(200),
    product_name    VARCHAR2(500),
    company_name    VARCHAR2(500),
    source          VARCHAR2(200),
    source_url      VARCHAR2(1000),
    country         VARCHAR2(100),
    origin_country  VARCHAR2(100),
    created_at      DATE            DEFAULT SYSDATE
);

COMMENT ON COLUMN capture_data_fda.id IS '主键，数据唯一ID';
COMMENT ON COLUMN capture_data_fda.batch_id IS '关联批次ID';
COMMENT ON COLUMN capture_data_fda.task_id IS '关联任务配置ID';
COMMENT ON COLUMN capture_data_fda.title IS '任务标题';
COMMENT ON COLUMN capture_data_fda.summary IS '任务摘要';
COMMENT ON COLUMN capture_data_fda.publish_date IS '发布日期';
COMMENT ON COLUMN capture_data_fda.category IS '食品类别';
COMMENT ON COLUMN capture_data_fda.risk_factor IS '风险因子';
COMMENT ON COLUMN capture_data_fda.product_name IS '产品名称';
COMMENT ON COLUMN capture_data_fda.company_name IS '生产企业';
COMMENT ON COLUMN capture_data_fda.source IS '信息来源';
COMMENT ON COLUMN capture_data_fda.source_url IS '原始链接';
COMMENT ON COLUMN capture_data_fda.country IS '通报国别';
COMMENT ON COLUMN capture_data_fda.origin_country IS '产地国别';
COMMENT ON COLUMN capture_data_fda.created_at IS '记录创建时间';


-- 食品抽检信息查询分析系统数据表
CREATE TABLE capture_data_rybzszh (
    id              NUMBER(20)      PRIMARY KEY,
    batch_id        VARCHAR2(50)    NOT NULL,
    task_id         NUMBER(10)      NOT NULL,
    item_id         VARCHAR2(50),
    title           VARCHAR2(500),
    summary         VARCHAR2(2000),
    publish_date    VARCHAR2(50),
    category        VARCHAR2(200),
    product_name    VARCHAR2(500),
    spec_model      VARCHAR2(200),
    production_date VARCHAR2(200),
    company_name    VARCHAR2(500),
    risk_factor     VARCHAR2(200),
    test_result     VARCHAR2(500),
    unqualified_item_standard VARCHAR2(1000),
    source          VARCHAR2(200),
    source_url      VARCHAR2(1000),
    risk_platform_url VARCHAR2(1000),
    country         VARCHAR2(100),
    origin_country  VARCHAR2(100),
    created_at      DATE            DEFAULT SYSDATE
);

COMMENT ON COLUMN capture_data_rybzszh.id IS '主键，数据唯一ID';
COMMENT ON COLUMN capture_data_rybzszh.batch_id IS '关联批次ID';
COMMENT ON COLUMN capture_data_rybzszh.task_id IS '关联任务配置ID';
COMMENT ON COLUMN capture_data_rybzszh.item_id IS '项目ID';
COMMENT ON COLUMN capture_data_rybzszh.title IS '任务标题';
COMMENT ON COLUMN capture_data_rybzszh.summary IS '任务摘要';
COMMENT ON COLUMN capture_data_rybzszh.publish_date IS '发布日期';
COMMENT ON COLUMN capture_data_rybzszh.category IS '食品类别';
COMMENT ON COLUMN capture_data_rybzszh.product_name IS '产品名称';
COMMENT ON COLUMN capture_data_rybzszh.spec_model IS '规格型号';
COMMENT ON COLUMN capture_data_rybzszh.production_date IS '生产日期/批号';
COMMENT ON COLUMN capture_data_rybzszh.company_name IS '生产企业';
COMMENT ON COLUMN capture_data_rybzszh.risk_factor IS '风险因子';
COMMENT ON COLUMN capture_data_rybzszh.test_result IS '检验结果';
COMMENT ON COLUMN capture_data_rybzszh.unqualified_item_standard IS '不合格项目（标准值）';
COMMENT ON COLUMN capture_data_rybzszh.source IS '信息来源';
COMMENT ON COLUMN capture_data_rybzszh.source_url IS '原始链接';
COMMENT ON COLUMN capture_data_rybzszh.risk_platform_url IS '风险项目检测平台链接';
COMMENT ON COLUMN capture_data_rybzszh.country IS '通报国别';
COMMENT ON COLUMN capture_data_rybzszh.origin_country IS '产地国别';
COMMENT ON COLUMN capture_data_rybzszh.created_at IS '记录创建时间'; 


-- 万方期刊数据表
CREATE TABLE capture_data_wanfang_magazine (
    id              NUMBER(20)      PRIMARY KEY,
    batch_id        VARCHAR2(50)    NOT NULL,
    task_id         NUMBER(10)      NOT NULL,
    sources    VARCHAR2(500),
    title           VARCHAR2(500),
    summary         VARCHAR2(4000),
    publish_year    VARCHAR2(10),
    issue_num       VARCHAR2(10),
    publish_date    VARCHAR2(50),
    source_url      VARCHAR2(1000) ,
    country         VARCHAR2(100),
    origin_country  VARCHAR2(100),
    created_at      DATE            DEFAULT SYSDATE
);

COMMENT ON COLUMN capture_data_wanfang_magazine.id IS '主键，数据唯一ID';
COMMENT ON COLUMN capture_data_wanfang_magazine.batch_id IS '关联批次ID';
COMMENT ON COLUMN capture_data_wanfang_magazine.task_id IS '关联任务配置ID';
COMMENT ON COLUMN capture_data_wanfang_magazine.sources IS '信息来源';
COMMENT ON COLUMN capture_data_wanfang_magazine.title IS '任务标题';
COMMENT ON COLUMN capture_data_wanfang_magazine.summary IS '任务摘要';
COMMENT ON COLUMN capture_data_wanfang_magazine.publish_year IS '发表年份';
COMMENT ON COLUMN capture_data_wanfang_magazine.issue_num IS '期号';
COMMENT ON COLUMN capture_data_wanfang_magazine.publish_date IS '发表日期';
COMMENT ON COLUMN capture_data_wanfang_magazine.source_url IS '原始链接';
COMMENT ON COLUMN capture_data_wanfang_magazine.country IS '通报国别';
COMMENT ON COLUMN capture_data_wanfang_magazine.origin_country IS '产地国别';
COMMENT ON COLUMN capture_data_wanfang_magazine.created_at IS '记录创建时间'; 