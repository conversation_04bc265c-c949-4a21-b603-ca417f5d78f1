
import requests
import math
from bs4 import BeautifulSoup
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from datetime import datetime
from dateutil.relativedelta import relativedelta

# 登录信息
login_url = "https://std.nctid.cn/home/<USER>/login.html"
account = "<EMAIL>"
password = "fagui@001"

login_data = {
    "account": account,
    "password": password
}

session = requests.Session()

headers = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/115.0.0.0 Safari/537.36",
    "Accept": "*/*",
    "Referer": "https://std.nctid.cn/",
    "Origin": "https://std.nctid.cn",
    "Accept-Language": "zh-CN,zh;q=0.9",
    "Connection": "keep-alive"
}

# 登录
resp = session.post(login_url, data=login_data, headers=headers)
if resp.status_code != 200 or resp.json().get("code") != 1:
    print("❌ 登录失败")
    exit(1)


today = datetime.today()
etime = today.strftime("%Y-%m-%d")
stime = (today - relativedelta(months=1)).strftime("%Y-%m-%d")

#一年
# etime = today.strftime("%Y-%m-%d")
# stime = (today - relativedelta(years=1)).strftime("%Y-%m-%d")

# 公共参数
base_payload = {
    'catid': '89,90,94,95,96,97,98,99,100,102,622,239,119,120,121,122,123,124,125',
    'hege': '2',
    'stime': stime,
    'etime': etime,
    'page_size': '20',
    'active': 'cn'
}

# 获取总数
count_url = "https://std.nctid.cn/cj/search/getcounts.html"
count_resp = session.post(count_url, data=base_payload, headers=headers)
total_count = int(count_resp.json()["data"]["count"])
page_size = int(base_payload["page_size"])
total_pages = math.ceil(total_count / page_size)

# 抓取所有 itemid
all_items = []
data_url = "https://std.nctid.cn/cj/search/cnselect.html"

for page in range(1, total_pages + 1):
    payload = base_payload.copy()
    payload["page_num"] = str(page)
    resp = session.post(data_url, data=payload, headers=headers)
    items = resp.json().get("data", {}).get("data", [])
    all_items.extend(items)

print(f"共获取 {len(all_items)} 条 itemid，开始抓取详情...")

# 详情页解析函数
def parse_detail(itemid, base_info):
    url = f"https://std.nctid.cn/cj/search/lookinfo.html?active=cn&id={itemid}"
    resp = session.get(url, headers=headers)
    soup = BeautifulSoup(resp.text, "html.parser")

    table = soup.select_one("table.layui-table")
    data = {
        "itemid": itemid,
        "信息来源": "食品抽检信息查询分析系统",
        "通报国别": "中国",
        "产地国别": "中国"
    }

    # 临时变量：用于拼接 不合格项目（标准值）
    reason = ""
    standard_limit = ""
    judge_result = ""  # 判定结果

    for tr in table.select("tr"):
        tds = tr.find_all("td")
        if len(tds) < 2:
            continue
        key = tds[0].get_text(strip=True).replace("：", "").replace(" ", "")
        value_cell = tds[1]
        value = value_cell.get_text(strip=True)

        # 处理链接字段
        if "来源链接" in key:
            a = value_cell.find("a")
            if a and a.has_attr("href"):
                data["原始链接"] = a["href"]
        elif "伙伴网链接" in key:
            a = value_cell.find("a")
            if a and a.has_attr("href"):
                data["风险项目检测平台-链接"] = a["href"]

        # 字段匹配填入
        if key == "产品分类":
            data["食品类别"] = value.split("-->")[0].strip()
        elif key == "产品名称":
            data["产品名称"] = value
        elif key == "规格":
            data["规格型号"] = value
        elif key == "生产时间":
            data["生产日期/批号"] = value
        elif key == "生产企业名称":
            data["生产企业"] = value
        elif key == "不合格原因":
            reason = value
            data["风险因子"] = value
        elif key == "检测结果":
            data["检验结果"] = value
        elif key == "标准/法规限值":
            standard_limit = value
        elif key == "通报时间":
            data["发布日期"] = value
        elif key == "判定结果":
            judge_result = value

    # 拼接字段
    if reason and standard_limit:
        data["不合格项目（标准值）"] = f"{reason}（{standard_limit}）"

    # 构造任务标题与摘要（使用 all_items 中数据）
    tbdw = base_info.get("tbdw", "")
    scname = base_info.get("scname", "")
    title = base_info.get("title", "")
    hege = base_info.get("hege", "")
    data["任务标题"] = f"{tbdw}通报{scname}生产的{title}{reason}{hege}"
    data["任务摘要"] = data["任务标题"]

    return data



def parse_detail_safe(item):
    itemid = item.get("itemid")
    try:
        detail = parse_detail(itemid, item)
        print(f"✅ 成功抓取 itemid={itemid}")
        return detail
    except Exception as e:
        print(f"❌ 抓取失败 itemid={itemid}，原因：{e}")
        return None


# 控制线程数（建议根据网络情况调整 5～15 之间）
max_workers = 10

all_details = []
with ThreadPoolExecutor(max_workers=max_workers) as executor:
    futures = [executor.submit(parse_detail_safe, item) for item in all_items]

    for future in as_completed(futures):
        result = future.result()
        if result:
            all_details.append(result)

# 最后输出结果
from pprint import pprint
pprint(all_details, width=160)
