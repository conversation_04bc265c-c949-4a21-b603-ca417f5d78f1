import abc
import datetime
import logging
import requests
import time
from config import Config
from models.data_batch import DataBatch
from models.data_record import DataRecord

class BaseCrawler(abc.ABC):
    """
    爬虫基类，定义了爬虫的通用接口和方法
    """
    
    def __init__(self, task_id, batch_id=None):
        """
        初始化爬虫
        
        Args:
            task_id (int): 任务ID
            batch_id (str, optional): 批次ID，如果不提供则自动生成
        """
        self.task_id = task_id
        self.logger = logging.getLogger(f"crawler.{self.__class__.__name__}")
        
        # 创建或获取批次
        if batch_id:
            self.batch = DataBatch.get_by_id(batch_id)
            if not self.batch:
                self.batch = DataBatch(
                    batch_id=batch_id,
                    task_id=task_id,
                    capture_time=datetime.datetime.now(),
                    status=DataBatch.STATUS_RUNNING
                )
        else:
            self.batch = DataBatch.create_new_batch(task_id)
            
        # 保存批次信息
        self.batch.save()
        
        # 初始化会话
        self.session = requests.Session()
        self.session.headers.update({
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/115.0.0.0 Safari/537.36"
        })
        
        # 初始化统计信息
        self.stats = {
            "start_time": None,
            "end_time": None,
            "success_count": 0,
            "failed_count": 0,
            "total_count": 0
        }
    
    def run(self):
        """
        运行爬虫
        
        Returns:
            bool: 抓取成功返回True，否则返回False
        """
        try:
            self.logger.info(f"开始抓取任务 {self.task_id}, 批次 {self.batch.batch_id}")
            self.stats["start_time"] = datetime.datetime.now()
            
            # 执行抓取
            success = self._crawl()
            
            self.stats["end_time"] = datetime.datetime.now()
            duration = (self.stats["end_time"] - self.stats["start_time"]).total_seconds()
            
            # 更新批次状态
            if success:
                self.batch.status = DataBatch.STATUS_SUCCESS
                self.batch.record_count = self.stats["success_count"]
                self.batch.remark = f"抓取成功，耗时 {duration:.2f} 秒，成功 {self.stats['success_count']} 条，失败 {self.stats['failed_count']} 条"
            else:
                self.batch.status = DataBatch.STATUS_FAILED
                self.batch.remark = f"抓取失败，耗时 {duration:.2f} 秒，成功 {self.stats['success_count']} 条，失败 {self.stats['failed_count']} 条"
                
            self.batch.save()
            
            self.logger.info(f"抓取任务完成: {self.batch.remark}")
            return success
        except Exception as e:
            self.logger.error(f"抓取任务异常: {e}")
            self.batch.status = DataBatch.STATUS_FAILED
            self.batch.remark = f"抓取异常: {str(e)}"
            self.batch.save()
            return False
    
    def make_request(self, url, method="GET", params=None, data=None, json=None, headers=None, timeout=None, retries=None):
        """
        发送HTTP请求，支持重试
        
        Args:
            url (str): 请求URL
            method (str, optional): 请求方法，默认为GET
            params (dict, optional): 查询参数
            data (dict, optional): 表单数据
            json (dict, optional): JSON数据
            headers (dict, optional): 请求头
            timeout (int, optional): 超时时间，默认使用配置值
            retries (int, optional): 重试次数，默认使用配置值
            
        Returns:
            requests.Response: 响应对象，如果请求失败则返回None
        """
        timeout = timeout or Config.REQUEST_TIMEOUT
        retries = retries or Config.MAX_RETRIES
        
        for i in range(retries + 1):
            try:
                response = self.session.request(
                    method=method,
                    url=url,
                    params=params,
                    data=data,
                    json=json,
                    headers=headers,
                    timeout=timeout
                )
                response.raise_for_status()
                return response
            except requests.RequestException as e:
                self.logger.warning(f"请求失败 ({i+1}/{retries+1}): {url}, 错误: {e}")
                if i < retries:
                    time.sleep(Config.RETRY_DELAY)
                else:
                    return None
    
    def save_records(self, records, model_class=None):
        """
        保存抓取的记录
        
        Args:
            records (list): 记录列表，每个记录应该是一个字典
            model_class (class, optional): 用于实例化记录的数据模型类，默认为 DataRecord
            
        Returns:
            tuple: (成功数量, 失败数量)
        """
        if not records:
            return 0, 0
            
        # 如果未指定模型类，则使用默认的 DataRecord
        if model_class is None:
            model_class = DataRecord
            
        # 转换为指定模型类的对象
        data_objects = []
        for record in records:
            data_object = model_class(
                batch_id=self.batch.batch_id,
                task_id=self.task_id,
                **record
            )
            data_objects.append(data_object)
            
        # 批量插入
        # 假设所有模型类都有一个 bulk_insert 静态方法
        success_count, fail_count = model_class.bulk_insert(data_objects)
        
        # 更新统计信息
        self.stats["success_count"] += success_count
        self.stats["failed_count"] += fail_count
        self.stats["total_count"] += len(records)
        
        return success_count, fail_count
    
    @abc.abstractmethod
    def _crawl(self):
        """
        执行抓取逻辑，由子类实现
        
        Returns:
            bool: 抓取成功返回True，否则返回False
        """
        pass 