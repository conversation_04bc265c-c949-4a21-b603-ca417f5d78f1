import datetime
from utils.db import get_connection

class DataRYBZSZH:
    """
    食品抽检信息查询分析系统数据模型，对应数据库表 CAPTURE_DATA_RYBZSZH
    """
    
    def __init__(self, id=None, batch_id=None, task_id=None, **kwargs):
        """
        初始化数据记录对象
        
        Args:
            id (int, optional): 记录ID
            batch_id (str, optional): 批次ID
            task_id (int, optional): 任务ID
            **kwargs: 其他字段
        """
        self.id = id
        self.batch_id = batch_id
        self.task_id = task_id
        self.item_id = kwargs.get('item_id')
        self.title = kwargs.get('title')
        self.summary = kwargs.get('summary')
        self.publish_date = kwargs.get('publish_date')
        self.category = kwargs.get('category')
        self.product_name = kwargs.get('product_name')
        self.spec_model = kwargs.get('spec_model')
        self.production_date = kwargs.get('production_date')
        self.company_name = kwargs.get('company_name')
        self.risk_factor = kwargs.get('risk_factor')
        self.test_result = kwargs.get('test_result')
        self.unqualified_item_standard = kwargs.get('unqualified_item_standard')
        self.source = kwargs.get('source')
        self.source_url = kwargs.get('source_url')
        self.risk_platform_url = kwargs.get('risk_platform_url')
        self.country = kwargs.get('country')
        self.origin_country = kwargs.get('origin_country')
        self.created_at = kwargs.get('created_at') or datetime.datetime.now()
    
    @staticmethod
    def get_by_id(record_id):
        """
        根据ID获取数据记录
        
        Args:
            record_id (int): 记录ID
            
        Returns:
            DataRYBZSZH: 数据记录对象，如果不存在则返回None
        """
        conn = None
        cursor = None
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute(
                "SELECT id, batch_id, task_id, item_id, title, summary, publish_date, "
                "category, product_name, spec_model, production_date, company_name, "
                "risk_factor, test_result, unqualified_item_standard, source, "
                "source_url, risk_platform_url, country, origin_country, created_at "
                "FROM capture_data_rybzszh WHERE id = :id",
                {"id": record_id}
            )
            row = cursor.fetchone()
            if row:
                return DataRYBZSZH(
                    id=row[0],
                    batch_id=row[1],
                    task_id=row[2],
                    item_id=row[3],
                    title=row[4],
                    summary=row[5],
                    publish_date=row[6],
                    category=row[7],
                    product_name=row[8],
                    spec_model=row[9],
                    production_date=row[10],
                    company_name=row[11],
                    risk_factor=row[12],
                    test_result=row[13],
                    unqualified_item_standard=row[14],
                    source=row[15],
                    source_url=row[16],
                    risk_platform_url=row[17],
                    country=row[18],
                    origin_country=row[19],
                    created_at=row[20]
                )
            return None
        finally:
            if cursor:
                cursor.close()
            if conn:
                conn.close()
    
    @staticmethod
    def get_by_batch_id(batch_id, limit=100, offset=0):
        """
        根据批次ID获取数据记录列表
        
        Args:
            batch_id (str): 批次ID
            limit (int, optional): 返回的最大记录数，默认为100
            offset (int, optional): 起始偏移量，默认为0
            
        Returns:
            tuple: (记录列表, 总记录数)
        """
        conn = None
        cursor = None
        try:
            conn = get_connection()
            cursor = conn.cursor()
            
            # 获取总记录数
            cursor.execute(
                "SELECT COUNT(*) FROM capture_data_rybzszh WHERE batch_id = :batch_id",
                {"batch_id": batch_id}
            )
            total = cursor.fetchone()[0]
            
            # 获取分页数据
            cursor.execute(
                "SELECT * FROM ("
                "  SELECT a.*, ROWNUM rn FROM ("
                "    SELECT id, batch_id, task_id, item_id, title, summary, publish_date, "
                "    category, product_name, spec_model, production_date, company_name, "
                "    risk_factor, test_result, unqualified_item_standard, source, "
                "    source_url, risk_platform_url, country, origin_country, created_at "
                "    FROM capture_data_rybzszh "
                "    WHERE batch_id = :batch_id "
                "    ORDER BY id"
                "  ) a WHERE ROWNUM <= :end_row"
                ") WHERE rn > :start_row",
                {"batch_id": batch_id, "start_row": offset, "end_row": offset + limit}
            )
            
            records = []
            for row in cursor.fetchall():
                record = DataRYBZSZH(
                    id=row[0],
                    batch_id=row[1],
                    task_id=row[2],
                    item_id=row[3],
                    title=row[4],
                    summary=row[5],
                    publish_date=row[6],
                    category=row[7],
                    product_name=row[8],
                    spec_model=row[9],
                    production_date=row[10],
                    company_name=row[11],
                    risk_factor=row[12],
                    test_result=row[13],
                    unqualified_item_standard=row[14],
                    source=row[15],
                    source_url=row[16],
                    risk_platform_url=row[17],
                    country=row[18],
                    origin_country=row[19],
                    created_at=row[20]
                )
                records.append(record)
            
            return records, total
        finally:
            if cursor:
                cursor.close()
            if conn:
                conn.close()
    
    def save(self):
        """
        保存数据记录到数据库
        
        Returns:
            bool: 保存成功返回True，否则返回False
        """
        conn = None
        cursor = None
        try:
            conn = get_connection()
            cursor = conn.cursor()
            
            if self.id is None:
                # 插入新记录
                cursor.execute(
                    "SELECT NVL(MAX(id), 0) + 1 FROM capture_data_rybzszh"
                )
                self.id = cursor.fetchone()[0]
                
                cursor.execute(
                    "INSERT INTO capture_data_rybzszh ("
                    "id, batch_id, task_id, item_id, title, summary, publish_date, "
                    "category, product_name, spec_model, production_date, company_name, "
                    "risk_factor, test_result, unqualified_item_standard, source, "
                    "source_url, risk_platform_url, country, origin_country, created_at"
                    ") VALUES ("
                    ":id, :batch_id, :task_id, :item_id, :title, :summary, :publish_date, "
                    ":category, :product_name, :spec_model, :production_date, :company_name, "
                    ":risk_factor, :test_result, :unqualified_item_standard, :source, "
                    ":source_url, :risk_platform_url, :country, :origin_country, :created_at"
                    ")",
                    {
                        "id": self.id,
                        "batch_id": self.batch_id,
                        "task_id": self.task_id,
                        "item_id": self.item_id,
                        "title": self.title,
                        "summary": self.summary,
                        "publish_date": self.publish_date,
                        "category": self.category,
                        "product_name": self.product_name,
                        "spec_model": self.spec_model,
                        "production_date": self.production_date,
                        "company_name": self.company_name,
                        "risk_factor": self.risk_factor,
                        "test_result": self.test_result,
                        "unqualified_item_standard": self.unqualified_item_standard,
                        "source": self.source,
                        "source_url": self.source_url,
                        "risk_platform_url": self.risk_platform_url,
                        "country": self.country,
                        "origin_country": self.origin_country,
                        "created_at": self.created_at
                    }
                )
            else:
                # 更新现有记录
                cursor.execute(
                    "UPDATE capture_data_rybzszh SET "
                    "batch_id = :batch_id, "
                    "task_id = :task_id, "
                    "item_id = :item_id, "
                    "title = :title, "
                    "summary = :summary, "
                    "publish_date = :publish_date, "
                    "category = :category, "
                    "product_name = :product_name, "
                    "spec_model = :spec_model, "
                    "production_date = :production_date, "
                    "company_name = :company_name, "
                    "risk_factor = :risk_factor, "
                    "test_result = :test_result, "
                    "unqualified_item_standard = :unqualified_item_standard, "
                    "source = :source, "
                    "source_url = :source_url, "
                    "risk_platform_url = :risk_platform_url, "
                    "country = :country, "
                    "origin_country = :origin_country "
                    "WHERE id = :id",
                    {
                        "batch_id": self.batch_id,
                        "task_id": self.task_id,
                        "item_id": self.item_id,
                        "title": self.title,
                        "summary": self.summary,
                        "publish_date": self.publish_date,
                        "category": self.category,
                        "product_name": self.product_name,
                        "spec_model": self.spec_model,
                        "production_date": self.production_date,
                        "company_name": self.company_name,
                        "risk_factor": self.risk_factor,
                        "test_result": self.test_result,
                        "unqualified_item_standard": self.unqualified_item_standard,
                        "source": self.source,
                        "source_url": self.source_url,
                        "risk_platform_url": self.risk_platform_url,
                        "country": self.country,
                        "origin_country": self.origin_country,
                        "id": self.id
                    }
                )
            
            conn.commit()
            return True
        except Exception as e:
            if conn:
                conn.rollback()
            print(f"保存数据记录失败: {e}")
            return False
        finally:
            if cursor:
                cursor.close()
            if conn:
                conn.close()
    
    @staticmethod
    def bulk_insert(records):
        """
        批量插入数据记录
        
        Args:
            records (list): 数据记录对象列表
            
        Returns:
            tuple: (成功数量, 失败数量)
        """
        if not records:
            return 0, 0
            
        conn = None
        cursor = None
        try:
            conn = get_connection()
            cursor = conn.cursor()
            
            # 获取起始ID
            cursor.execute(
                "SELECT NVL(MAX(id), 0) + 1 FROM capture_data_rybzszh"
            )
            start_id = cursor.fetchone()[0]
            
            success_count = 0
            fail_count = 0
            
            for i, record in enumerate(records):
                try:
                    record.id = start_id + i
                    
                    cursor.execute(
                        "INSERT INTO capture_data_rybzszh ("
                        "id, batch_id, task_id, item_id, title, summary, publish_date, "
                        "category, product_name, spec_model, production_date, company_name, "
                        "risk_factor, test_result, unqualified_item_standard, source, "
                        "source_url, risk_platform_url, country, origin_country, created_at"
                        ") VALUES ("
                        ":id, :batch_id, :task_id, :item_id, :title, :summary, :publish_date, "
                        ":category, :product_name, :spec_model, :production_date, :company_name, "
                        ":risk_factor, :test_result, :unqualified_item_standard, :source, "
                        ":source_url, :risk_platform_url, :country, :origin_country, :created_at"
                        ")",
                        {
                            "id": record.id,
                            "batch_id": record.batch_id,
                            "task_id": record.task_id,
                            "item_id": record.item_id,
                            "title": record.title,
                            "summary": record.summary,
                            "publish_date": record.publish_date,
                            "category": record.category,
                            "product_name": record.product_name,
                            "spec_model": record.spec_model,
                            "production_date": record.production_date,
                            "company_name": record.company_name,
                            "risk_factor": record.risk_factor,
                            "test_result": record.test_result,
                            "unqualified_item_standard": record.unqualified_item_standard,
                            "source": record.source,
                            "source_url": record.source_url,
                            "risk_platform_url": record.risk_platform_url,
                            "country": record.country,
                            "origin_country": record.origin_country,
                            "created_at": record.created_at
                        }
                    )
                    success_count += 1
                except Exception as e:
                    print(f"插入记录失败: {e}")
                    fail_count += 1
            
            conn.commit()
            return success_count, fail_count
        except Exception as e:
            if conn:
                conn.rollback()
            print(f"批量插入数据记录失败: {e}")
            return 0, len(records)
        finally:
            if cursor:
                cursor.close()
            if conn:
                conn.close()
                
    def to_dict(self):
        """
        将对象转换为字典
        
        Returns:
            dict: 包含对象属性的字典
        """
        return {
            "id": self.id,
            "batch_id": self.batch_id,
            "task_id": self.task_id,
            "item_id": self.item_id,
            "title": self.title,
            "summary": self.summary,
            "publish_date": self.publish_date,
            "category": self.category,
            "product_name": self.product_name,
            "spec_model": self.spec_model,
            "production_date": self.production_date,
            "company_name": self.company_name,
            "risk_factor": self.risk_factor,
            "test_result": self.test_result,
            "unqualified_item_standard": self.unqualified_item_standard,
            "source": self.source,
            "source_url": self.source_url,
            "risk_platform_url": self.risk_platform_url,
            "country": self.country,
            "origin_country": self.origin_country,
            "created_at": self.created_at
        } 