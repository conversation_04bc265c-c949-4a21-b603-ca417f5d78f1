document.addEventListener('DOMContentLoaded', function() {
    // 初始化提示工具
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // 详情按钮点击事件
    const detailModal = new bootstrap.Modal(document.getElementById('recordDetailModal'));
    const detailButtons = document.querySelectorAll('.view-details');
    
    detailButtons.forEach(button => {
        button.addEventListener('click', function() {
            const recordId = this.getAttribute('data-record-id');
            const taskType = this.getAttribute('data-task-type');
            
            // 显示加载中
            document.getElementById('loadingSpinner').style.display = 'block';
            document.getElementById('recordDetailContent').style.display = 'none';
            
            // 打开弹窗
            detailModal.show();
            
            // 获取详情数据
            fetch(`/api/record_detail/${taskType}/${recordId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // 隐藏加载中
                        document.getElementById('loadingSpinner').style.display = 'none';
                        
                        // 显示详情内容
                        const detailContent = document.getElementById('recordDetailContent');
                        detailContent.style.display = 'block';
                        
                        // 加载对应类型的详情模板
                        loadDetailTemplate(taskType, data.data);
                        
                        // 设置原始链接
                        const sourceUrl = data.data.source_url || '#';
                        document.getElementById('sourceUrlLink').href = sourceUrl;
                        if (sourceUrl === '#') {
                            document.getElementById('sourceUrlLink').style.display = 'none';
                        } else {
                            document.getElementById('sourceUrlLink').style.display = 'inline-block';
                        }
                    } else {
                        document.getElementById('loadingSpinner').style.display = 'none';
                        document.getElementById('recordDetailContent').style.display = 'block';
                        document.getElementById('recordDetailContent').innerHTML = 
                            `<div class="alert alert-danger">获取详情失败: ${data.message}</div>`;
                    }
                })
                .catch(error => {
                    document.getElementById('loadingSpinner').style.display = 'none';
                    document.getElementById('recordDetailContent').style.display = 'block';
                    document.getElementById('recordDetailContent').innerHTML = 
                        `<div class="alert alert-danger">获取详情出错: ${error}</div>`;
                });
        });
    });
    
    // 加载详情模板
    function loadDetailTemplate(taskType, data) {
        const detailContent = document.getElementById('recordDetailContent');
        
        // 直接使用回退渲染函数，不再尝试加载模板
        // 因为HTML模板需要服务器端渲染，而我们这里只是客户端JavaScript
        detailContent.innerHTML = renderFallbackDetail(taskType, data);
    }
    
    // 简单的模板渲染函数（替换变量）
    function renderTemplate(template, context) {
        // 这里使用一个简单的正则表达式来替换模板中的变量
        // 实际项目中可能需要使用更复杂的模板引擎
        return template.replace(/\{\{\s*data\.([^}]+)\s*\|\|\s*'([^']+)'\s*\}\}/g, function(match, key, defaultValue) {
            return context.data[key] || defaultValue;
        });
    }
    
    // 回退渲染函数（直接生成HTML）
    function renderFallbackDetail(taskType, data) {
        if (taskType === 'fda') {
            return createFdaDetailHtml(data);
        } else if (taskType === 'rybzszh') {
            return createRybzszhDetailHtml(data);
        } else if (taskType === 'wanfang_magazine') {
            return createWanfangDetailHtml(data);
        } else {
            return `<div class="alert alert-warning">未知的数据类型: ${taskType}</div>`;
        }
    }
    
    // 创建FDA数据详情HTML
    function createFdaDetailHtml(data) {
        return `
            <div class="row mb-3">
                <div class="col-md-12">
                    <h5 class="border-bottom pb-2">${data.title || '无标题'}</h5>
                </div>
            </div>
            <div class="row mb-3">
                <div class="col-md-6">
                    <p><strong>信息来源:</strong> ${data.source || '-'}</p>
                    <p><strong>发布日期:</strong> ${data.publish_date || '-'}</p>
                    <p><strong>食品类别:</strong> ${data.category || '-'}</p>
                    <p><strong>风险因子:</strong> ${data.risk_factor || '-'}</p>
                    <p><strong>通报国别:</strong> ${data.country || '-'}</p>
                    <p><strong>产地国别:</strong> ${data.origin_country || '-'}</p>
                </div>
                <div class="col-md-6">
                    <p><strong>产品名称:</strong> ${data.product_name || '-'}</p>
                    <p><strong>生产企业:</strong> ${data.company_name || '-'}</p>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <strong>任务摘要</strong>
                        </div>
                        <div class="card-body">
                            <div class="long-text-content">${data.summary || '无摘要信息'}</div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
    
    // 创建乳业标准数字化详情HTML
    function createRybzszhDetailHtml(data) {
        return `
            <div class="row mb-3">
                <div class="col-md-12">
                    <h5 class="border-bottom pb-2">${data.title || '无标题'}</h5>
                </div>
            </div>
            <div class="row mb-3">
                <div class="col-md-6">
                    <p><strong>信息来源:</strong> ${data.source || '-'}</p>
                    <p><strong>发布日期:</strong> ${data.publish_date || '-'}</p>
                    <p><strong>食品类别:</strong> ${data.category || '-'}</p>
                    <p><strong>风险因子:</strong> ${data.risk_factor || '-'}</p>
                    <p><strong>通报国别:</strong> ${data.country || '-'}</p>
                    <p><strong>产地国别:</strong> ${data.origin_country || '-'}</p>
                </div>
                <div class="col-md-6">
                    <p><strong>产品名称:</strong> ${data.product_name || '-'}</p>
                    <p><strong>规格型号:</strong> ${data.spec_model || '-'}</p>
                    <p><strong>生产日期/批号:</strong> ${data.production_date || '-'}</p>
                    <p><strong>生产企业:</strong> ${data.company_name || '-'}</p>
                    <p><strong>检验结果:</strong> ${data.test_result || '-'}</p>
                </div>
            </div>
            <div class="row mb-3">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <strong>不合格项目（标准值）</strong>
                        </div>
                        <div class="card-body">
                            <div class="long-text-content">${data.unqualified_item_standard || '无数据'}</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <strong>任务摘要</strong>
                        </div>
                        <div class="card-body">
                            <div class="long-text-content">${data.summary || '无摘要信息'}</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-md-12">
                    <p><strong>风险项目检测平台链接:</strong> 
                        <a href="${data.risk_platform_url || '#'}" target="_blank">${data.risk_platform_url || '无链接'}</a>
                    </p>
                </div>
            </div>
        `;
    }
    
    // 创建万方期刊数据详情HTML
    function createWanfangDetailHtml(data) {
        return `
            <div class="row mb-3">
                <div class="col-md-12">
                    <h5 class="border-bottom pb-2">${data.title || '无标题'}</h5>
                </div>
            </div>
            <div class="row mb-3">
                <div class="col-md-6">
                    <p><strong>信息来源:</strong> ${data.sources || '-'}</p>
                    <p><strong>发表年份:</strong> ${data.publish_year || '-'}</p>
                    <p><strong>期号:</strong> ${data.issue_num || '-'}</p>
                    <p><strong>发布日期:</strong> ${data.publish_date || '-'}</p>
                </div>
                <div class="col-md-6">
                    <p><strong>通报国别:</strong> ${data.country || '-'}</p>
                    <p><strong>产地国别:</strong> ${data.origin_country || '-'}</p>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <strong>任务摘要</strong>
                        </div>
                        <div class="card-body">
                            <div class="long-text-content">${data.summary || '无摘要信息'}</div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
}); 