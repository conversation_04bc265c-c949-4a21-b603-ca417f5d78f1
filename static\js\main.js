/**
 * 数据抓取管理平台 - 主JavaScript文件
 */

// 暗黑模式切换功能
document.addEventListener('DOMContentLoaded', function() {
    // 检查用户偏好
    const currentTheme = localStorage.getItem('theme') || 'light';
    if (currentTheme === 'dark') {
        document.body.setAttribute('data-bs-theme', 'dark');
    }

    // 主题切换按钮
    const themeToggler = document.getElementById('theme-toggle');
    if (themeToggler) {
        themeToggler.addEventListener('click', function() {
            const currentTheme = document.body.getAttribute('data-bs-theme') || 'light';
            const newTheme = currentTheme === 'light' ? 'dark' : 'light';
            
            document.body.setAttribute('data-bs-theme', newTheme);
            localStorage.setItem('theme', newTheme);
            
            // 更新图标
            updateThemeIcon(newTheme);
        });
        
        // 初始化图标
        updateThemeIcon(currentTheme);
    }
});

// 更新主题图标
function updateThemeIcon(theme) {
    const themeIcon = document.getElementById('theme-icon');
    if (themeIcon) {
        if (theme === 'dark') {
            themeIcon.classList.remove('bi-moon');
            themeIcon.classList.add('bi-sun');
        } else {
            themeIcon.classList.remove('bi-sun');
            themeIcon.classList.add('bi-moon');
        }
    }
}

// 初始化统计图表
function initCharts() {
    // 成功率图表
    const successRateElement = document.getElementById('success-rate-chart');
    if (successRateElement) {
        const successRate = parseFloat(successRateElement.getAttribute('data-success-rate') || 0);
        
        new Chart(successRateElement, {
            type: 'doughnut',
            data: {
                labels: ['成功', '失败'],
                datasets: [{
                    data: [successRate, 100 - successRate],
                    backgroundColor: ['rgba(102, 187, 106, 0.8)', 'rgba(239, 83, 80, 0.8)'],
                    borderColor: ['rgb(76, 175, 80)', 'rgb(229, 57, 53)'],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return context.label + ': ' + context.raw + '%';
                            }
                        }
                    }
                },
                cutout: '70%'
            }
        });
    }
    
    // 任务统计图表
    const taskStatsElement = document.getElementById('task-stats-chart');
    if (taskStatsElement) {
        const taskCount = parseInt(taskStatsElement.getAttribute('data-task-count') || 0);
        const enabledTaskCount = parseInt(taskStatsElement.getAttribute('data-enabled-task-count') || 0);
        
        new Chart(taskStatsElement, {
            type: 'bar',
            data: {
                labels: ['任务总数', '启用任务'],
                datasets: [{
                    label: '任务统计',
                    data: [taskCount, enabledTaskCount],
                    backgroundColor: ['rgba(38, 166, 154, 0.8)', 'rgba(102, 187, 106, 0.8)'],
                    borderColor: ['rgb(0, 137, 123)', 'rgb(76, 175, 80)'],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            precision: 0
                        }
                    }
                }
            }
        });
    }
}

// 表格排序和搜索功能
document.addEventListener('DOMContentLoaded', function() {
    // 初始化DataTables（如果页面上有表格）
    const dataTables = document.querySelectorAll('.data-table');
    if (dataTables.length > 0) {
        dataTables.forEach(table => {
            $(table).DataTable({
                language: {
                    url: '/static/i18n/zh.json'
                },
                responsive: true,
                pageLength: 10,
                lengthMenu: [[10, 25, 50, -1], [10, 25, 50, "全部"]]
            });
        });
    }
    
    // 初始化图表
    if (typeof Chart !== 'undefined') {
        initCharts();
    }
    
    // 初始化提示工具
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

// 表单验证
(function() {
    'use strict';
    
    // 获取所有需要验证的表单
    const forms = document.querySelectorAll('.needs-validation');
    
    // 循环并阻止提交
    Array.prototype.slice.call(forms).forEach(function(form) {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            
            form.classList.add('was-validated');
        }, false);
    });
})(); 