from DrissionPage import Chromium,ChromiumOptions
import time

def extract_article_info(tab):
    """
    抓取网页中的文章信息
    只提取标题、内容、时间、跳转链接
    """
    articles_data = []

    # 等待页面加载完成 - 减少等待时间
    time.sleep(1)

    # 查找所有文章元素
    articles = tab.eles('tag:article')
    print(f"找到 {len(articles)} 篇文章")

    for i, article in enumerate(articles, 1):
        try:
            print(f"正在处理第 {i} 篇文章...")
            article_info = {}

            # 1. 抓取标题和链接 - 从h1标签中获取
            title_element = article.ele('.lxb_af-template_tags-get_linked_post_title-link', timeout=0.5)
            if title_element:
                article_info['title'] = title_element.text.strip()
                article_info['link'] = title_element.attr('href')
                print(f"  找到标题: {article_info['title']}")
                print(f"  找到标题链接: {article_info['link']}")
            else:
                article_info['title'] = "未找到标题"
                article_info['link'] = ""
                print(f"  未找到标题元素")

            # 2. 抓取时间 - 在header标签下查找
            time_element = article.ele('header time.lxb_af-template_tags-get_post_date', timeout=0.5)
            if time_element:
                article_info['time'] = time_element.text.strip()
                print(f"  找到时间: {article_info['time']}")
            else:
                # 备用方案：直接查找time标签
                time_element = article.ele('tag:time', timeout=0.5)
                if time_element:
                    article_info['time'] = time_element.text.strip()
                    print(f"  备用方案找到时间: {article_info['time']}")
                else:
                    article_info['time'] = "未找到时间"
                    print(f"  未找到时间元素")

            # 3. 抓取内容摘要 - 修复内容提取逻辑
            # 尝试多种选择器
            content_div = None
            selectors = [
                '.lxb_af-post_content.lxb_af-clear',  # 原始选择器
                '.lxb_af-post_content',               # 只用第一个类名
                'div.lxb_af-post_content',            # 明确指定div标签
                '[class*="lxb_af-post_content"]'      # 包含该类名的元素
            ]

            for selector in selectors:
                content_div = article.ele(selector, timeout=0.5)
                if content_div:
                    print(f"  使用选择器 '{selector}' 找到内容div")
                    break

            if content_div:
                # 获取所有段落元素
                paragraphs = content_div.eles('tag:p', timeout=0.5)
                print(f"  找到 {len(paragraphs)} 个段落")
                content_parts = []

                for j, p in enumerate(paragraphs):
                    p_text = p.text.strip()
                    print(f"    段落{j+1}: {p_text[:50]}..." if len(p_text) > 50 else f"    段落{j+1}: {p_text}")
                    if p_text:  # 如果段落有内容
                        content_parts.append(p_text)

                # 合并所有段落内容
                if content_parts:
                    article_info['content'] = '\n\n'.join(content_parts)
                    print(f"  成功提取内容，长度: {len(article_info['content'])}")
                else:
                    # 如果没有找到段落，尝试获取整个div的文本
                    full_text = content_div.text.strip()
                    print(f"  段落为空，尝试获取整个div文本: {full_text[:100]}...")
                    # 移除"Continue Reading"部分
                    if "Continue Reading" in full_text:
                        full_text = full_text.split("Continue Reading")[0].strip()
                    article_info['content'] = full_text
            else:
                print(f"  所有选择器都未找到内容div，尝试查找所有div")
                # 最后的备用方案：查找文章内所有div
                all_divs = article.eles('tag:div', timeout=0.5)
                print(f"  文章内共有 {len(all_divs)} 个div")
                for k, div in enumerate(all_divs):
                    div_class = div.attr('class') or ''
                    if 'post_content' in div_class or 'content' in div_class:
                        print(f"    div{k+1} class: {div_class}")
                        content_text = div.text.strip()
                        if content_text and len(content_text) > 50:  # 有实质内容
                            print(f"    找到可能的内容div: {content_text[:100]}...")
                            if "Continue Reading" in content_text:
                                content_text = content_text.split("Continue Reading")[0].strip()
                            article_info['content'] = content_text
                            break
                else:
                    article_info['content'] = "未找到内容"



            articles_data.append(article_info)

        except Exception as e:
            print(f"抓取第 {i} 篇文章时出错: {e}")
            continue

    return articles_data

def print_article_info(articles):
    """
    格式化打印文章信息
    """
    for i, article in enumerate(articles, 1):
        print(f"\n{'='*60}")
        print(f"文章 {i}")
        print(f"{'='*60}")
        print(f"标题: {article['title']}")
        print(f"时间: {article['time']}")
        print(f"内容: {article['content']}")
        print(f"跳转链接: {article['link']}")

def main():
    """
    主函数
    """
    # 创建浏览器实例，启用无头模式提高速度
    co = ChromiumOptions()
    # co.headless()
    browser = Chromium(co)
    tab = browser.latest_tab

    try:
        # 访问目标网页
        print("正在访问网页...")
        start_time = time.time()
        tab.get('https://www.foodsafetynews.com/sections/food-recalls/')
        print(f"页面加载耗时: {time.time() - start_time:.2f}秒")

        # 抓取文章信息
        print("正在抓取文章信息...")
        extract_start = time.time()
        articles = extract_article_info(tab)
        print(f"信息抓取耗时: {time.time() - extract_start:.2f}秒")

        # 打印结果
        print(f"\n共找到 {len(articles)} 篇文章")
        print_article_info(articles)

        print(f"\n总耗时: {time.time() - start_time:.2f}秒")

    except Exception as e:
        print(f"程序执行出错: {e}")

    finally:
        # 自动关闭浏览器
        print("\n抓取完成，正在关闭浏览器...")
        browser.quit()

if __name__ == "__main__":
    main()