#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
New Food Magazine数据抓取脚本
使用Selenium抓取https://www.newfoodmagazine.com/core_topic/food-safety/页面的文章数据
"""

import re
import time
from datetime import datetime, timedelta
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from bs4 import BeautifulSoup


class NewFoodMagazineScraper:
    def __init__(self):
        self.base_url = self.build_url_with_date_range()
        self.driver = None
        self.setup_driver()

    def build_url_with_date_range(self):
        """构建包含动态日期范围的URL"""
        # 获取当前日期
        today = datetime.now()
        # 计算一个月前的日期
        one_month_ago = today - timedelta(days=30)

        # 格式化日期为YYYY-MM-DD格式
        start_date = one_month_ago.strftime('%Y-%m-%d')
        end_date = today.strftime('%Y-%m-%d')

        # 构建URL
        base_url = "https://www.newfoodmagazine.com/core_topic/food-safety/"
        date_range = f"fwp_date_range={start_date}%2C{end_date}"
        full_url = f"{base_url}?{date_range}"

        print(f"查询日期范围: {start_date} 到 {end_date}")
        print(f"构建的URL: {full_url}")

        return full_url

    def setup_driver(self):
        options = Options()
        # options.add_argument('--headless')
        options.add_argument('--disable-gpu')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)
        options.add_argument("user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")

        self.driver = webdriver.Chrome(options=options)

        self.driver.execute_cdp_cmd("Page.addScriptToEvaluateOnNewDocument", {
            "source": """
            Object.defineProperty(navigator, 'webdriver', {
              get: () => undefined
            });
            """
        })

    def extract_article_data(self, article_element):
        try:
            title_link = article_element.find('h3').find('a') if article_element.find('h3') else None
            if not title_link:
                return None

            title = title_link.get_text(strip=True)
            article_url = title_link.get('href', '')

            meta_p = article_element.find('p', class_='meta')
            date = ''
            author = ''

            if meta_p:
                meta_text = meta_p.get_text(strip=True)
                if '|' in meta_text:
                    parts = meta_text.split('|')
                    if len(parts) >= 2:
                        date = parts[0].strip()
                        author_part = parts[1].strip()
                        if author_part.startswith('By '):
                            author = author_part[3:].strip()
                else:
                    date = meta_text

            excerpt = ''
            excerpt_p = article_element.find('p', class_='listOnly')
            if excerpt_p:
                excerpt = excerpt_p.get_text(strip=True)

            return {
                'title': title,
                'date': date,
                'author': author,
                'excerpt': excerpt,
                'url': article_url
            }

        except Exception as e:
            print(f"提取文章数据时出错: {e}")
            return None

    def get_total_pages(self):
        try:
            self.driver.get(self.base_url)
            time.sleep(3)

            soup = BeautifulSoup(self.driver.page_source, 'html.parser')

            # 查找所有包含facetwp-pager类的div
            pagers = soup.find_all('div', class_='facetwp-pager')

            page_numbers = []

            for pager in pagers:
                # 查找所有包含facetwp-page类的a标签
                page_links = pager.find_all('a', class_='facetwp-page')

                for link in page_links:
                    classes = link.get('class', [])

                    # 跳过next/prev链接，但包含first/last链接
                    if 'next' in classes or 'prev' in classes:
                        continue

                    page_num = link.get('data-page')
                    if page_num and page_num.isdigit():
                        page_numbers.append(int(page_num))
                        print(f"找到页码: {page_num}, 类: {classes}")

            if page_numbers:
                total_pages = max(page_numbers)
                print(f"检测到总页数: {total_pages}")
                return total_pages

            # 如果没有找到分页，检查是否有文章
            articles = soup.find_all('article')
            if articles:
                print(f"未找到分页信息，但检测到 {len(articles)} 篇文章，默认为1页")
            else:
                print("未找到分页信息和文章，默认为1页")

            return 1

        except Exception as e:
            print(f"获取总页数时出错: {e}")
            return 1

    def scrape_page(self, page_num):
        try:
            if page_num == 1:
                url = self.base_url
            else:
                url = f"{self.base_url}&fwp_paged={page_num}"

            print(f"正在访问: {url}")
            self.driver.get(url)
            time.sleep(3)

            try:
                WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.TAG_NAME, "article"))
                )
            except:
                pass

            soup = BeautifulSoup(self.driver.page_source, 'html.parser')
            articles = soup.find_all('article')

            article_data = []
            for article in articles:
                data = self.extract_article_data(article)
                if data:
                    article_data.append(data)

            return article_data

        except Exception as e:
            print(f"抓取页面时出错: {e}")
            return []

    def scrape_all_articles(self, max_pages=None):
        all_articles = []

        try:
            total_pages = self.get_total_pages()

            if max_pages:
                total_pages = min(total_pages, max_pages)
                print(f"限制抓取页数为: {total_pages}")

            for page in range(1, total_pages + 1):
                print(f"\n正在抓取第 {page}/{total_pages} 页...")
                articles = self.scrape_page(page)
                all_articles.extend(articles)

                print(f"第{page}页抓取到 {len(articles)} 篇文章，累计 {len(all_articles)} 篇")

                if len(articles) == 0 and page > 1:
                    print("当前页无数据，可能已到达末尾，停止抓取")
                    break

                if total_pages > 1:
                    progress = (page / total_pages) * 100
                    print(f"进度: {progress:.1f}%")

                if page < total_pages:
                    time.sleep(2)

        except KeyboardInterrupt:
            print(f"\n用户中断，已抓取 {len(all_articles)} 篇文章")
        except Exception as e:
            print(f"抓取过程中出错: {e}")

        return all_articles

    def close(self):
        if self.driver:
            self.driver.quit()


def main():
    print("New Food Magazine文章抓取脚本")
    print("自动查询最近一个月的食品安全文章")
    print("-" * 50)

    MAX_PAGES = None  # 设置为2测试分页功能

    if MAX_PAGES:
        print(f"配置: 限制抓取前 {MAX_PAGES} 页")
    else:
        print("配置: 抓取所有页面")

    print("-" * 50)

    scraper = NewFoodMagazineScraper()

    try:
        # 先测试分页检测
        print("测试分页检测...")
        total_pages = scraper.get_total_pages()
        print(f"分页检测结果: {total_pages} 页")

        if total_pages > 1:
            print("分页检测成功，开始抓取...")
        else:
            print("只有1页或分页检测失败")

        articles = scraper.scrape_all_articles(max_pages=MAX_PAGES)

        print(f"\n" + "=" * 50)
        print(f"抓取完成！总共获取到 {len(articles)} 篇文章")
        print("=" * 50)

        for article in articles:
            print(f"标题: {article['title']}")
            print(f"日期: {article['date']}")
            print(f"作者: {article['author']}")
            print(f"摘要: {article['excerpt']}")
            print(f"链接: {article['url']}")
            print("-" * 60)

    finally:
        scraper.close()


if __name__ == "__main__":
    main()