import datetime
from utils.db import get_connection

class DataWanfangMagazine:
    """
    万方期刊数据模型，对应数据库表 CAPTURE_DATA_WANFANG_MAGAZINE
    """
    
    def __init__(self, id=None, batch_id=None, task_id=None, **kwargs):
        """
        初始化数据记录对象
        """
        self.id = id
        self.batch_id = batch_id
        self.task_id = task_id
        self.sources = kwargs.get('sources')
        self.title = kwargs.get('title')
        self.summary = kwargs.get('summary')
        self.publish_year = kwargs.get('publish_year')
        self.issue_num = kwargs.get('issue_num')
        self.publish_date = kwargs.get('publish_date')
        self.source_url = kwargs.get('source_url')
        self.country = kwargs.get('country')
        self.origin_country = kwargs.get('origin_country')
        self.created_at = kwargs.get('created_at') or datetime.datetime.now()

    def save(self):
        """
        保存单条数据记录到数据库
        """
        conn = None
        cursor = None
        try:
            conn = get_connection()
            cursor = conn.cursor()
            
            if self.id is None:
                cursor.execute("SELECT NVL(MAX(id), 0) + 1 FROM capture_data_wanfang_magazine")
                self.id = cursor.fetchone()[0]
                
                sql = """
                    INSERT INTO capture_data_wanfang_magazine (
                        id, batch_id, task_id, title, summary, 
                        publish_year, issue_num, publish_date, source_url, created_at, country, origin_country,sources
                    ) VALUES (
                        :id, :batch_id, :task_id, :title, :summary,
                        :publish_year, :issue_num, :publish_date, :source_url, :created_at, :country, :origin_country, :sources
                    )
                """
                params = self.__dict__
                cursor.execute(sql, params)
            else:
                # 更新逻辑暂不实现，因为通常是插入新数据
                pass
            
            conn.commit()
            return True
        except Exception as e:
            if conn:
                conn.rollback()
            print(f"保存万方期刊数据失败: {e}")
            return False
        finally:
            if cursor:
                cursor.close()
            if conn:
                conn.close()

    @staticmethod
    def bulk_insert(records):
        """
        批量插入数据记录
        """
        if not records:
            return 0, 0
            
        conn = None
        cursor = None
        success_count = 0
        fail_count = 0
        
        try:
            conn = get_connection()
            cursor = conn.cursor()
            
            # 获取起始ID
            cursor.execute("SELECT NVL(MAX(id), 0) + 1 FROM capture_data_wanfang_magazine")
            next_id = cursor.fetchone()[0]
            
            data_to_insert = []
            for record in records:
                record.id = next_id
                data_to_insert.append(record.__dict__)
                next_id += 1
            
            sql = """
                INSERT INTO capture_data_wanfang_magazine (
                    id, batch_id, task_id, title, summary, 
                    publish_year, issue_num, publish_date, source_url, created_at, country,  origin_country, sources
                ) VALUES (
                    :id, :batch_id, :task_id, :title, :summary,
                    :publish_year, :issue_num, :publish_date, :source_url, :created_at, :country,  :origin_country, :sources
                )
            """
            
            cursor.executemany(sql, data_to_insert, batcherrors=True)
            
            # 检查批量插入错误
            errors = cursor.getbatcherrors()
            if errors:
                fail_count = len(errors)
                # 可以选择性地记录错误详情
                # for error in errors:
                #     print(f"Error: {error.message} at row offset: {error.offset}")
            
            success_count = len(records) - fail_count
            conn.commit()
            
        except Exception as e:
            if conn:
                conn.rollback()
            print(f"批量插入万方期刊数据失败: {e}")
            fail_count = len(records) # 如果整个批次失败
            success_count = 0
        finally:
            if cursor:
                cursor.close()
            if conn:
                conn.close()
        
        return success_count, fail_count 