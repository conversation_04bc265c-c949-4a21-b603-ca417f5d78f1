from datetime import datetime

class Utils:
    """
    一个提供常用工具方法的类。
    """

    @staticmethod
    def format_date_string(date_str: str, input_format: str, output_format: str) -> str:
        """
        将日期字符串从一种格式转换为另一种格式。

        Args:
            date_str (str): 要转换的日期字符串。
            input_format (str): 输入日期字符串的当前格式（例如："%Y-%m-%d"）。
            output_format (str): 目标输出日期字符串的格式（例如："%Y年%m月%d日"）。

        Returns:
            str: 格式化后的日期字符串，如果转换失败则返回空字符串。
        """
        try:
            date_object = datetime.strptime(date_str, input_format)
            return date_object.strftime(output_format)
        except ValueError:
            print(f"错误: 无法根据输入格式 '{input_format}' 解析日期字符串 '{date_str}'。")
            return ""

    @staticmethod
    def reverse_string(s: str) -> str:
        """
        反转给定的字符串。

        Args:
            s (str): 要反转的字符串。

        Returns:
            str: 反转后的字符串。
        """
        return s[::-1]

    @staticmethod
    def calculate_string_length_without_spaces(s: str) -> int:
        """
        计算字符串的实际长度，忽略其中的空格。

        Args:
            s (str): 要计算长度的字符串。

        Returns:
            int: 不含空格的字符串长度。
        """
        return len(s.replace(" ", ""))

# --- 使用示例 ---
if __name__ == "__main__":
    print("--- 日期格式转换 ---")
    date_example = "July 09, 2025"
    # 将 "July 09, 2025" 转换为 "2025年07月09日"
    formatted_date = Utils.format_date_string(date_example, "%B %d, %Y", "%Y年%m月%d日")
    print(f"'{date_example}' 转换为: {formatted_date}")

    date_example_2 = "2025-01-15"
    # 将 "2025-01-15" 转换为 "Jan 15, 2025"
    formatted_date_2 = Utils.format_date_string(date_example_2, "%Y-%m-%d", "%b %d, %Y")
    print(f"'{date_example_2}' 转换为: {formatted_date_2}")

    print("\n--- 字符串反转 ---")
    str_to_reverse = "Hello World"
    reversed_str = Utils.reverse_string(str_to_reverse)
    print(f"'{str_to_reverse}' 反转后是: '{reversed_str}'")

    print("\n--- 计算字符串长度（不含空格） ---")
    str_with_spaces = "  Python Programming  "
    length_no_spaces = Utils.calculate_string_length_without_spaces(str_with_spaces)
    print(f"字符串 '{str_with_spaces}' (不含空格) 的长度是: {length_no_spaces}")

    str_no_spaces = "HelloWorld"
    length_no_spaces_2 = Utils.calculate_string_length_without_spaces(str_no_spaces)
    print(f"字符串 '{str_no_spaces}' (不含空格) 的长度是: {length_no_spaces_2}")