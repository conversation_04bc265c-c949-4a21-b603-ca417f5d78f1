{% extends 'base.html' %}

{% block title %}任务管理 - 数据抓取管理平台{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4 fade-in">
    <div>
        <h2 class="mb-1">任务管理</h2>
        <p class="text-muted">配置和管理数据抓取任务</p>
    </div>
    <a href="{{ url_for('tasks.task_form') }}" class="btn btn-primary">
        <i class="bi bi-plus-lg me-1"></i> 添加任务
    </a>
</div>

<div class="card fade-in">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover align-middle data-table">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>任务名称</th>
                        <th>Cron表达式</th>
                        <th>状态</th>
                        <th>创建时间</th>
                        <th>下次执行时间</th>
                        <th style="width: 200px;">操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for task in tasks %}
                    <tr>
                        <td>{{ task.id }}</td>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="task-icon me-2">
                                    <i class="bi bi-list-task text-primary"></i>
                                </div>
                                <div>
                                    <div class="fw-bold">{{ task.task_name }}</div>
                                </div>
                            </div>
                        </td>
                        <td><code>{{ task.schedule_cron }}</code></td>
                        <td>
                            {% if task.is_enabled == 1 %}
                            <span class="badge bg-success">启用</span>
                            {% else %}
                            <span class="badge bg-secondary">禁用</span>
                            {% endif %}
                        </td>
                        <td>{{ task.created_at.strftime('%Y-%m-%d %H:%M:%S') if task.created_at else '' }}</td>
                        <td>
                            {% if task_status.get(task.id) and task_status[task.id].next_run_time %}
                            <div class="d-flex align-items-center">
                                <i class="bi bi-clock text-success me-1"></i>
                                <span>{{ task_status[task.id].next_run_time.strftime('%Y-%m-%d %H:%M:%S') }}</span>
                            </div>
                            {% else %}
                            <span class="text-muted">未调度</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group">
                                <a href="{{ url_for('tasks.task_form', task_id=task.id) }}" class="btn btn-sm btn-outline-primary" data-bs-toggle="tooltip" title="编辑任务">
                                    <i class="bi bi-pencil"></i>
                                </a>
                                <a href="{{ url_for('tasks.task_toggle', task_id=task.id) }}" class="btn btn-sm btn-outline-warning" data-bs-toggle="tooltip" 
                                   title="{% if task.is_enabled == 1 %}禁用任务{% else %}启用任务{% endif %}">
                                    <i class="bi bi-{% if task.is_enabled == 1 %}toggle-on{% else %}toggle-off{% endif %}"></i>
                                </a>
                                <a href="{{ url_for('tasks.task_run', task_id=task.id) }}" class="btn btn-sm btn-outline-success run-task-btn" data-bs-toggle="tooltip" title="立即执行">
                                    <i class="bi bi-play-fill"></i>
                                </a>
                                <a href="{{ url_for('tasks.task_delete', task_id=task.id) }}" class="btn btn-sm btn-outline-danger" 
                                   onclick="return confirm('确定要删除此任务吗？')" data-bs-toggle="tooltip" title="删除任务">
                                    <i class="bi bi-trash"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% else %}
                    <tr>
                        <td colspan="7" class="text-center py-4">
                            <div class="text-muted">
                                <i class="bi bi-inbox fs-2 d-block mb-2"></i>
                                暂无任务
                            </div>
                            <a href="{{ url_for('tasks.task_form') }}" class="btn btn-sm btn-primary mt-2">
                                <i class="bi bi-plus-lg me-1"></i> 添加第一个任务
                            </a>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<div class="card mt-4 fade-in">
    <div class="card-header">
        <h5 class="mb-0">Cron表达式帮助</h5>
    </div>
    <div class="card-body">
        <p>Cron表达式由5个或6个字段组成，格式为：<code>分 时 日 月 周 [年]</code></p>
        <div class="table-responsive">
            <table class="table table-sm">
                <thead>
                    <tr>
                        <th>字段</th>
                        <th>允许值</th>
                        <th>特殊字符</th>
                        <th>示例</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>分钟</td>
                        <td>0-59</td>
                        <td><code>* , - /</code></td>
                        <td><code>0</code>: 每小时的第0分钟</td>
                    </tr>
                    <tr>
                        <td>小时</td>
                        <td>0-23</td>
                        <td><code>* , - /</code></td>
                        <td><code>*/2</code>: 每2小时</td>
                    </tr>
                    <tr>
                        <td>日期</td>
                        <td>1-31</td>
                        <td><code>* , - / ? L W</code></td>
                        <td><code>1</code>: 每月1日</td>
                    </tr>
                    <tr>
                        <td>月份</td>
                        <td>1-12</td>
                        <td><code>* , - /</code></td>
                        <td><code>*</code>: 每月</td>
                    </tr>
                    <tr>
                        <td>星期</td>
                        <td>0-6 或 SUN-SAT</td>
                        <td><code>* , - / ? L #</code></td>
                        <td><code>1-5</code>: 周一至周五</td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card bg-light">
                    <div class="card-header">
                        <h6 class="mb-0">常用示例</h6>
                    </div>
                    <div class="card-body">
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <span>每天午夜执行</span>
                                <code>0 0 * * *</code>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <span>每2小时执行一次</span>
                                <code>0 */2 * * *</code>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <span>每个工作日上午9点执行</span>
                                <code>0 9 * * 1-5</code>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <span>每月1日午夜执行</span>
                                <code>0 0 1 * *</code>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card bg-light">
                    <div class="card-header">
                        <h6 class="mb-0">特殊字符说明</h6>
                    </div>
                    <div class="card-body">
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item"><code>*</code> - 表示所有可能的值</li>
                            <li class="list-group-item"><code>,</code> - 用于列举值，如 "1,3,5"</li>
                            <li class="list-group-item"><code>-</code> - 表示范围，如 "1-5"</li>
                            <li class="list-group-item"><code>/</code> - 表示增量，如 "0/15" 表示从0开始每15个单位</li>
                            <li class="list-group-item"><code>?</code> - 用于日期和星期字段，表示不指定值</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 初始化提示工具
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
        
        // 为"立即执行"按钮添加点击事件
        const runTaskButtons = document.querySelectorAll('.run-task-btn');
        runTaskButtons.forEach(function(button) {
            button.addEventListener('click', function(e) {
                // 防止重复点击
                if (this.classList.contains('disabled')) {
                    e.preventDefault();
                    return false;
                }
                
                // 禁用按钮
                this.classList.add('disabled');
                this.setAttribute('aria-disabled', 'true');
                
                // 更改按钮内容，显示加载状态
                const icon = this.querySelector('i');
                icon.classList.remove('bi-play-fill');
                icon.classList.add('bi-hourglass-split');
                
                // 添加加载提示文本
                const tooltip = bootstrap.Tooltip.getInstance(this);
                if (tooltip) {
                    tooltip.dispose();
                }
                this.setAttribute('title', '任务执行中...');
                new bootstrap.Tooltip(this).show();
                
                // 允许表单提交，页面将会重定向
                return true;
            });
        });
    });
</script>
{% endblock %} 