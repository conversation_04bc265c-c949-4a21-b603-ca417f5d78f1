<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}数据抓取管理平台{% endblock %}</title>
    <!-- Bootstrap 5.3 CSS -->
    <link href="{{ url_for('static', filename='css/bootstrap.min.css') }}" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <!-- <link rel="stylesheet" href="https://cdn.staticfile.org/bootstrap-icons/1.11.1/font/bootstrap-icons.css"> -->
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/bootstrap-icons/1.11.1/font/bootstrap-icons.css">

    <!-- DataTables CSS -->
    <link href="{{ url_for('static', filename='css/dataTables.bootstrap5.min.css') }}" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/responsive.bootstrap5.min.css') }}" rel="stylesheet">
    <!-- 自定义样式 -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <style>
        /* 自定义导航栏颜色 */
        .navbar-custom {
            background-color: var(--primary-color);
        }
        .navbar-custom .navbar-brand,
        .navbar-custom .nav-link {
            color: white;
        }
        .navbar-custom .nav-link:hover {
            color: rgba(255, 255, 255, 0.85);
        }
    </style>
    {% block head %}{% endblock %}
</head>
<body>
    <!-- 顶部导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark navbar-custom sticky-top">
        <div class="container-fluid">
            <a class="navbar-brand d-flex align-items-center" href="{{ url_for('index.index') }}">
                <i class="bi bi-cloud-download me-2"></i>
                数据抓取管理平台
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav"
                    aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('index.index') }}">
                            <i class="bi bi-house-door"></i> 首页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('tasks.task_list') }}">
                            <i class="bi bi-list-task"></i> 任务管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('batches.batch_list') }}">
                            <i class="bi bi-collection"></i> 批次管理
                        </a>
                    </li>
                </ul>
                <div class="d-flex">
                    <button id="theme-toggle" class="btn btn-outline-light" type="button" aria-label="切换主题">
                        <i id="theme-icon" class="bi bi-moon"></i>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <div class="container-fluid py-4">
        <!-- 消息提示 -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <!-- 页面内容 -->
        {% block content %}{% endblock %}
    </div>

    <!-- 页脚 -->
    <footer class="footer py-3 mt-auto border-top">
        <div class="container-fluid text-center">
            <span class="text-muted">&copy; <span id="year"></span> 数据抓取管理平台</span>
        </div>
    </footer>


    <script>
        // 获取当前年份并插入到 span#year 中
        document.getElementById('year').textContent = new Date().getFullYear();
    </script>

    <!-- JavaScript 依赖 -->
    <!-- jQuery -->
    <script src="{{ url_for('static', filename='js/jquery-3.7.0.min.js') }}"></script>
    <!-- Bootstrap JS -->
    <script src="{{ url_for('static', filename='js/bootstrap.bundle.min.js') }}"></script>
    <!-- Chart.js -->
    <script src="{{ url_for('static', filename='js/chart.umd.min.js') }}"></script>
    <!-- DataTables JS -->
    <script src="{{ url_for('static', filename='js/jquery.dataTables.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/dataTables.bootstrap5.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/dataTables.responsive.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/responsive.bootstrap5.min.js') }}"></script>
    <!-- 自定义JS -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    {% block scripts %}{% endblock %}
</body>
</html> 