#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CFSA风险评估报告分类抓取脚本
抓取https://www.cfsa.net.cn/fxpg/fxpgbg/wsw/index.shtml页面的风险评估报告分类信息
"""

import requests
from bs4 import BeautifulSoup
import time
from urllib.parse import urljoin
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


class CFSARiskAssessmentScraper:
    def __init__(self):
        self.base_url = "https://www.cfsa.net.cn"
        self.target_url = "https://www.cfsa.net.cn/fxpg/fxpgbg/wsw/index.shtml"
        self.session = requests.Session()

        # 设置请求头，模拟浏览器访问
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })

    def fetch_page(self, url):
        """获取网页内容"""
        try:
            # 禁用SSL证书验证来解决证书问题
            response = self.session.get(url, timeout=30, verify=False)
            response.raise_for_status()
            response.encoding = 'utf-8'
            return response.text
        except requests.RequestException as e:
            print(f"请求失败: {e}")
            return None

    def parse_risk_assessment_categories(self, html_content):
        """解析风险评估报告分类信息"""
        soup = BeautifulSoup(html_content, 'html.parser')
        categories = []

        # 根据提供的HTML结构，查找包含风险评估报告的li元素
        # 查找title="风险评估报告"的a标签
        risk_assessment_links = soup.find_all('a', title='风险评估报告')

        for link in risk_assessment_links:
            # 找到包含该链接的父级li元素
            parent_li = link.find_parent('li')
            if parent_li:
                # 查找该li下的ul.left-secondnav
                second_nav = parent_li.find('ul', class_='left-secondnav')
                if second_nav:
                    # 提取所有子分类
                    sub_categories = second_nav.find_all('li')
                    for sub_li in sub_categories:
                        sub_link = sub_li.find('a')
                        if sub_link:
                            category_info = {
                                'title': sub_link.get('title', '').strip(),
                                'text': sub_link.get_text().strip(),
                                'href': sub_link.get('href', '').strip(),
                                'full_url': urljoin(self.base_url, sub_link.get('href', '')) if sub_link.get('href') else '',
                                'class': sub_link.get('class', [])
                            }
                            categories.append(category_info)
                    break  # 找到第一个匹配的就退出

        return categories

    def parse_report_list(self, html_content):
        """解析报告列表页面的内容"""
        soup = BeautifulSoup(html_content, 'html.parser')
        reports = []

        # 查找ul.news-list
        news_list = soup.find('ul', class_='news-list')
        if news_list:
            # 提取所有li元素
            list_items = news_list.find_all('li')
            for li in list_items:
                # 查找a标签和日期span
                link = li.find('a')
                date_span = li.find('span')

                if link and date_span:
                    report_info = {
                        'title': link.get('title', '').strip() or link.get_text().strip(),
                        'href': link.get('href', '').strip(),
                        'full_url': urljoin(self.base_url, link.get('href', '')) if link.get('href') else '',
                        'date': date_span.get_text().strip()
                    }
                    reports.append(report_info)

        return reports

    def scrape_category_reports(self, category_url, category_name):
        """抓取某个分类下的所有报告"""
        html_content = self.fetch_page(category_url)
        if not html_content:
            return []

        reports = self.parse_report_list(html_content)
        return reports

    def scrape_categories(self):
        """主要的抓取方法"""
        # 获取页面内容
        html_content = self.fetch_page(self.target_url)
        if not html_content:
            return []

        # 解析分类信息
        categories = self.parse_risk_assessment_categories(html_content)
        return categories

def main():
    """主函数"""
    scraper = CFSARiskAssessmentScraper()

    # 抓取分类信息
    categories = scraper.scrape_categories()

    all_reports = []

    if categories:
        for category in categories:
            # 抓取该分类下的报告列表
            reports = scraper.scrape_category_reports(category['full_url'], category['title'])

            for report in reports:
                report_dict = {
                    '类型': category['title'],
                    '标题': report['title'],
                    '时间': report['date'],
                    '链接': report['full_url']
                }
                all_reports.append(report_dict)

    # 输出所有报告
    for report in all_reports:
        print(report)


if __name__ == "__main__":
    main()