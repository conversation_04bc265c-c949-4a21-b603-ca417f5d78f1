import datetime
from utils.db import get_connection

class TaskConfig:
    """
    任务配置模型，对应数据库表 CAPTURE_TASK_CONFIG
    """
    
    def __init__(self, id=None, task_name=None, schedule_cron=None, is_enabled=1):
        """
        初始化任务配置对象
        
        Args:
            id (int, optional): 任务ID
            task_name (str, optional): 任务名称
            schedule_cron (str, optional): 定时任务cron表达式
            is_enabled (int, optional): 是否启用，1=启用，0=禁用
        """
        self.id = id
        self.task_name = task_name
        self.schedule_cron = schedule_cron
        self.is_enabled = is_enabled
        self.created_at = None
        self.updated_at = None
    
    @staticmethod
    def get_by_id(task_id):
        """
        根据ID获取任务配置
        
        Args:
            task_id (int): 任务ID
            
        Returns:
            TaskConfig: 任务配置对象，如果不存在则返回None
        """
        conn = None
        cursor = None
        try:
            conn = get_connection()
            cursor = conn.cursor()
            cursor.execute(
                "SELECT id, task_name, schedule_cron, is_enabled, created_at, updated_at "
                "FROM capture_task_config WHERE id = :id",
                {"id": task_id}
            )
            row = cursor.fetchone()
            if row:
                task = TaskConfig(
                    id=row[0],
                    task_name=row[1],
                    schedule_cron=row[2],
                    is_enabled=row[3]
                )
                task.created_at = row[4]
                task.updated_at = row[5]
                return task
            return None
        finally:
            if cursor:
                cursor.close()
            if conn:
                conn.close()
    
    @staticmethod
    def get_all(include_disabled=False):
        """
        获取所有任务配置
        
        Args:
            include_disabled (bool, optional): 是否包含已禁用的任务，默认为False
            
        Returns:
            list: 任务配置对象列表
        """
        conn = None
        cursor = None
        try:
            conn = get_connection()
            cursor = conn.cursor()
            
            sql = "SELECT id, task_name, schedule_cron, is_enabled, created_at, updated_at FROM capture_task_config"
            params = {}
            
            if not include_disabled:
                sql += " WHERE is_enabled = :is_enabled"
                params["is_enabled"] = 1
                
            sql += " ORDER BY id"
            
            cursor.execute(sql, params)
            
            tasks = []
            for row in cursor.fetchall():
                task = TaskConfig(
                    id=row[0],
                    task_name=row[1],
                    schedule_cron=row[2],
                    is_enabled=row[3]
                )
                task.created_at = row[4]
                task.updated_at = row[5]
                tasks.append(task)
            
            return tasks
        finally:
            if cursor:
                cursor.close()
            if conn:
                conn.close()
    
    def save(self):
        """
        保存任务配置到数据库
        
        Returns:
            bool: 保存成功返回True，否则返回False
        """
        conn = None
        cursor = None
        try:
            conn = get_connection()
            cursor = conn.cursor()
            
            now = datetime.datetime.now()
            
            if self.id is None:
                # 插入新记录
                cursor.execute(
                    "SELECT NVL(MAX(id), 0) + 1 FROM capture_task_config"
                )
                self.id = cursor.fetchone()[0]
                
                cursor.execute(
                    "INSERT INTO capture_task_config (id, task_name, schedule_cron, is_enabled, created_at) "
                    "VALUES (:id, :task_name, :schedule_cron, :is_enabled, :created_at)",
                    {
                        "id": self.id,
                        "task_name": self.task_name,
                        "schedule_cron": self.schedule_cron,
                        "is_enabled": self.is_enabled,
                        "created_at": now
                    }
                )
            else:
                # 更新现有记录
                cursor.execute(
                    "UPDATE capture_task_config SET "
                    "task_name = :task_name, "
                    "schedule_cron = :schedule_cron, "
                    "is_enabled = :is_enabled, "
                    "updated_at = :updated_at "
                    "WHERE id = :id",
                    {
                        "task_name": self.task_name,
                        "schedule_cron": self.schedule_cron,
                        "is_enabled": self.is_enabled,
                        "updated_at": now,
                        "id": self.id
                    }
                )
            
            conn.commit()
            return True
        except Exception as e:
            if conn:
                conn.rollback()
            print(f"保存任务配置失败: {e}")
            return False
        finally:
            if cursor:
                cursor.close()
            if conn:
                conn.close()
    
    def delete(self):
        """
        从数据库中删除任务配置
        
        Returns:
            bool: 删除成功返回True，否则返回False
        """
        if self.id is None:
            return False
            
        conn = None
        cursor = None
        try:
            conn = get_connection()
            cursor = conn.cursor()
            
            cursor.execute(
                "DELETE FROM capture_task_config WHERE id = :id",
                {"id": self.id}
            )
            
            conn.commit()
            return True
        except Exception as e:
            if conn:
                conn.rollback()
            print(f"删除任务配置失败: {e}")
            return False
        finally:
            if cursor:
                cursor.close()
            if conn:
                conn.close()
                
    def to_dict(self):
        """
        将对象转换为字典
        
        Returns:
            dict: 包含对象属性的字典
        """
        return {
            "id": self.id,
            "task_name": self.task_name,
            "schedule_cron": self.schedule_cron,
            "is_enabled": self.is_enabled,
            "created_at": self.created_at,
            "updated_at": self.updated_at
        } 