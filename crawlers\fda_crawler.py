import re
import certifi
import logging
from datetime import datetime, timedelta
from bs4 import BeautifulSoup
from utils.tencent_translate import translate_text
from utils.project_utils import Utils
from crawlers.base_crawler import BaseCrawler
from config import Config
class FDACrawler(BaseCrawler):
    """
    FDA 召回信息爬虫
    """

    # 列表页请求头
    LIST_HEADERS = {
        "User-Agent": "Apifox/1.0.0 (https://apifox.com)",
        "Accept": "*/*",
        "Referer": (
            "http://www.fda.gov/datatables/views/ajax?"
            "start=0&length=10&view_base_path=safety/recalls-"
            "market-withdrawals-safety-alerts/datatables-data&"
            "view_display_id=recall_datatable_block_1&"
            "view_dom_id=468f2a989f41f60305df7d10dce852e147d601cad167cf89728e4b38b5646899&"
            "view_name=recall_solr_index"
        ),
    }

    # 详情页请求头
    DETAIL_HEADERS = {
        "User-Agent": LIST_HEADERS["User-Agent"],
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
        "Referer": LIST_HEADERS["Referer"]
    }

    def __init__(self, task_id, batch_id=None, days=None):
        """
        初始化FDA爬虫
        
        Args:
            task_id (int): 任务ID
            batch_id (str, optional): 批次ID，如果不提供则自动生成
            days (int, optional): 抓取最近多少天的数据，默认使用配置值
        """
        super().__init__(task_id, batch_id)
        self.days = days or Config.CAPTURE_DAYS
        self.logger = logging.getLogger("crawler.FDACrawler")

        # 配置证书
        self.session.verify = certifi.where()

    def _crawl(self):
        """
        执行抓取逻辑
        
        Returns:
            bool: 抓取成功返回True，否则返回False
        """
        try:
            records = self._fetch_filtered_recalls(page_size=50)
            if records:
                self.save_records(records)
                return True
            return False
        except Exception as e:
            self.logger.error(f"FDA抓取失败: {e}")
            return False

    def _parse_recall_detail(self, url):
        """
        解析单条 recall 详情页，返回所需字段字典。

        Args:
            url (str): 详情页URL

        Returns:
            dict: 包含解析结果的字典，如果解析失败则返回空字典
        """
        response = self.make_request(url, headers=self.DETAIL_HEADERS)
        if not response:
            return {}

        if response.status_code in (301, 302) and "abuse-detection-apology" in response.headers.get("Location", ""):
            return {}

        soup = BeautifulSoup(response.text, "html.parser")

        # 信息来源、固定字段
        info = {
            "source": "美国食品和药物管理局（US FDA）",
            "source_url": url,
            "country": "美国",
            "origin_country": "美国"
        }

        # 标题提取
        h1 = soup.select_one("h1.content-title")
        original_title = h1.get_text(strip=True) if h1 else ""
        info["title"] = translate_text(original_title)

        # 摘要提取
        summary = ""
        announce_h2 = soup.select_one("h2#recall-announcement")
        if announce_h2:
            for sib in announce_h2.find_next_siblings():
                if sib.name == "p" or sib.find("strong"):
                    summary = sib.get_text(strip=True)
                    break
        info["summary"] = translate_text(summary)

        # 提取 <dl> 中结构化字段
        data = {}
        dl = soup.select_one("dl.lcds-description-list--grid")
        if dl:
            for dt, dd in zip(dl.find_all("dt"), dl.find_all("dd")):
                label = dt.get_text(" ", strip=True).rstrip(":")
                value = dd.get_text(" ", strip=True)
                data[label] = value

        # 映射字段
        info["publish_date"] = Utils.format_date_string(
            data.get("FDA Publish Date", ""), "%B %d, %Y", "%Y年%m月%d日")
        info["category"] = translate_text(data.get("Product Type", ""))
        info["risk_factor"] = translate_text(data.get("Reason for Announcement", ""))
        info["company_name"] = translate_text(data.get("Company Name", ""))

        # 从 Product Description 字段提取产品名
        product_name = data.get("Product Description", "").strip()

        # 如果开头是 "Product Description"（大小写都支持），就去掉这个前缀
        if product_name.lower().startswith("product description"):
            product_name = re.sub(r'^product description[:：]?\s*', '', product_name, flags=re.IGNORECASE)
        info["product_name"] = translate_text(product_name)

        return info

    def _fetch_filtered_recalls(self, page_size=50):
        """
        抓取近期内 recalls，排除指定分类，返回详情字段列表。
        
        Args:
            page_size (int, optional): 每页记录数，默认为50
            
        Returns:
            list: 记录列表
        """
        today = datetime.utcnow().date()
        start_date = today - timedelta(days=self.days)
        excluded = {"Drugs", "Medical Devices", "Animal & Veterinary"}

        api_url = "https://www.fda.gov/datatables/views/ajax"
        params = {
            "start": 0,
            "length": page_size,
            "view_base_path": "safety/recalls-market-withdrawals-safety-alerts/datatables-data",
            "view_display_id": "recall_datatable_block_1",
            "view_dom_id": "468f2a989f41f60305df7d10dce852e147d601cad167cf89728e4b38b5646899",
            "view_name": "recall_solr_index"
        }

        results = []
        while True:
            response = self.make_request(api_url, params=params, headers=self.LIST_HEADERS)
            if not response:
                break

            rows = response.json().get("data", [])
            if not rows:
                break

            stop = False
            for row in rows:
                # 列表页日期
                m = re.search(r'datetime="(\d{4}-\d{2}-\d{2})', row[0])
                if not m:
                    continue
                date = datetime.strptime(m.group(1), "%Y-%m-%d").date()
                if date < start_date:
                    stop = True
                    break

                # 过滤分类
                cat = row[3].strip()
                if date < start_date or date > today or cat in excluded:
                    continue

                # 提取详情链接
                link = re.search(r'<a href="([^"]+)">', row[1])
                if not link:
                    continue
                path = link.group(1)
                if not path.startswith("/safety/recalls-market-withdrawals-safety-alerts/"):
                    continue
                url = f"https://www.fda.gov{path}"

                # 解析详情并添加
                detail = self._parse_recall_detail(url)
                if detail:
                    results.append(detail)

            if stop:
                break
            params["start"] += page_size

        return results