"""
批次管理相关路由
"""
import logging
import csv
import io
import os
import urllib.parse
import codecs
from datetime import datetime
from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, Response, send_file
from utils.db import get_connection
from config import Config
from models.task_config import TaskConfig
from models.data_batch import DataBatch
from models.data_record import DataRecord
from models.data_rybzszh import DataRYBZSZH
from models.data_wanfang_magazine import DataWanfangMagazine


# 创建蓝图
batches_bp = Blueprint('batches', __name__)

@batches_bp.route('/batches')
def batch_list():
    """批次列表"""
    page = int(request.args.get('page', 1))
    selected_task_id = request.args.get('task_id', '')
    if selected_task_id:
        try:
            selected_task_id = int(selected_task_id)
        except ValueError:
            selected_task_id = ''
    
    # 获取所有任务
    tasks = TaskConfig.get_all(include_disabled=True)
    task_names = {task.id: task.task_name for task in tasks}
    
    # 获取批次数据
    conn = None
    cursor = None
    batches = []
    total_count = 0
    page_size = Config.DEFAULT_PAGE_SIZE
    
    try:
        conn = get_connection()
        cursor = conn.cursor()
        
        # 构建查询条件
        where_clause = ""
        params = {}
        
        if selected_task_id:
            where_clause = "WHERE task_id = :task_id"
            params["task_id"] = selected_task_id
        
        # 获取总记录数
        cursor.execute(
            f"SELECT COUNT(*) FROM capture_data_batch {where_clause}",
            params
        )
        total_count = cursor.fetchone()[0]
        
        # 计算分页
        offset = (page - 1) * page_size
        total_pages = (total_count + page_size - 1) // page_size if total_count > 0 else 1
        
        # 获取分页数据
        cursor.execute(
            f"SELECT * FROM ("
            f"  SELECT b.*, ROWNUM rn FROM ("
            f"    SELECT batch_id, task_id, capture_time, record_count, status, remark "
            f"    FROM capture_data_batch {where_clause} "
            f"    ORDER BY capture_time DESC"
            f"  ) b WHERE ROWNUM <= :end_row"
            f") WHERE rn > :start_row ORDER BY capture_time DESC",
            {**params, "start_row": offset, "end_row": offset + page_size}
        )
        
        for row in cursor.fetchall():
            batch = DataBatch(
                batch_id=row[0],
                task_id=row[1],
                capture_time=row[2],
                record_count=row[3],
                status=row[4],
                remark=row[5]
            )
            batches.append(batch)
    except Exception as e:
        logging.error(f"获取批次列表失败: {e}")
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()
    
    return render_template(
        "batch_list.html",
        batches=batches,
        tasks=tasks,
        task_names=task_names,
        selected_task_id=selected_task_id,
        page=page,
        total_pages=total_pages
    )

def get_task_type(task_name):
    """
    根据任务名称判断数据类型
    
    Args:
        task_name (str): 任务名称
        
    Returns:
        str: 数据类型，可能的值: 'fda', 'rybzszh', 'wanfang_magazine'
    """
    task_name = task_name.lower() if task_name else ""
    
    if "fda" in task_name:
        return "fda"
    elif "乳业" in task_name or "rybzszh" in task_name:
        return "rybzszh"
    elif "万方" in task_name or "wanfang" in task_name:
        return "wanfang_magazine"
    
    # 默认返回FDA类型
    return "fda"

def get_batch_records(batch_id, task_type, page=1, page_size=10):
    """
    根据批次ID和任务类型获取对应的数据记录
    
    Args:
        batch_id (str): 批次ID
        task_type (str): 任务类型
        page (int): 页码
        page_size (int): 每页记录数
        
    Returns:
        tuple: (记录列表, 总记录数)
    """
    if task_type == "rybzszh":
        return DataRYBZSZH.get_by_batch_id(batch_id, limit=page_size, offset=(page-1)*page_size)
    elif task_type == "wanfang_magazine":
        # 假设万方期刊数据模型也有类似的方法
        conn = None
        cursor = None
        try:
            conn = get_connection()
            cursor = conn.cursor()
            
            # 获取总记录数
            cursor.execute(
                "SELECT COUNT(*) FROM capture_data_wanfang_magazine WHERE batch_id = :batch_id",
                {"batch_id": batch_id}
            )
            total = cursor.fetchone()[0]
            
            # 获取分页数据
            cursor.execute(
                "SELECT * FROM ("
                "  SELECT a.*, ROWNUM rn FROM ("
                "    SELECT id, batch_id, task_id, sources, title, summary, publish_year, "
                "    issue_num, publish_date, source_url, country, origin_country, created_at "
                "    FROM capture_data_wanfang_magazine "
                "    WHERE batch_id = :batch_id "
                "    ORDER BY id"
                "  ) a WHERE ROWNUM <= :end_row"
                ") WHERE rn > :start_row",
                {"batch_id": batch_id, "start_row": (page-1)*page_size, "end_row": page*page_size}
            )
            
            records = []
            for row in cursor.fetchall():
                record = DataWanfangMagazine(
                    id=row[0],
                    batch_id=row[1],
                    task_id=row[2],
                    sources=row[3],
                    title=row[4],
                    summary=row[5],
                    publish_year=row[6],
                    issue_num=row[7],
                    publish_date=row[8],
                    source_url=row[9],
                    country=row[10],
                    origin_country=row[11],
                    created_at=row[12]
                )
                records.append(record)
            
            return records, total
        finally:
            if cursor:
                cursor.close()
            if conn:
                conn.close()
    else:
        # 默认返回FDA数据
        return DataRecord.get_by_batch_id(batch_id, limit=page_size, offset=(page-1)*page_size)

@batches_bp.route('/batches/<batch_id>')
def batch_detail(batch_id):
    """批次详情"""
    batch = DataBatch.get_by_id(batch_id)
    if not batch:
        flash("批次不存在", "danger")
        return redirect(url_for('batches.batch_list'))
    
    # 获取任务信息
    task = TaskConfig.get_by_id(batch.task_id)
    
    # 根据任务名称判断数据类型
    task_type = get_task_type(task.task_name if task else "")
    
    # 获取记录数据
    page = int(request.args.get('page', 1))
    page_size = Config.DEFAULT_PAGE_SIZE
    
    records, total_count = get_batch_records(batch_id, task_type, page, page_size)
    total_pages = (total_count + page_size - 1) // page_size if total_count > 0 else 1
    
    return render_template(
        "batch_detail.html",
        batch=batch,
        task=task,
        task_type=task_type,
        records=records,
        page=page,
        total_pages=total_pages
    )



@batches_bp.route('/batches/delete/<batch_id>')
def batch_delete(batch_id):
    """删除批次"""
    batch = DataBatch.get_by_id(batch_id)
    if not batch:
        flash("批次不存在", "danger")
        return redirect(url_for('batches.batch_list'))
    
    if batch.delete():
        flash("批次已删除", "success")
    else:
        flash("批次删除失败", "danger")
    
    return redirect(url_for('batches.batch_list'))

@batches_bp.route('/api/record_detail/<task_type>/<int:record_id>')
def record_detail_api(task_type, record_id):
    """获取记录详情API"""
    try:
        if task_type == "rybzszh":
            record = DataRYBZSZH.get_by_id(record_id)
            if record:
                return jsonify({
                    "success": True,
                    "data": record.to_dict()
                })
        elif task_type == "wanfang_magazine":
            # 假设万方期刊数据模型也有类似的方法
            conn = None
            cursor = None
            try:
                conn = get_connection()
                cursor = conn.cursor()
                cursor.execute(
                    "SELECT id, batch_id, task_id, sources, title, summary, publish_year, "
                    "issue_num, publish_date, source_url, country, origin_country, created_at "
                    "FROM capture_data_wanfang_magazine WHERE id = :id",
                    {"id": record_id}
                )
                row = cursor.fetchone()
                if row:
                    record = DataWanfangMagazine(
                        id=row[0],
                        batch_id=row[1],
                        task_id=row[2],
                        sources=row[3],
                        title=row[4],
                        summary=row[5],
                        publish_year=row[6],
                        issue_num=row[7],
                        publish_date=row[8],
                        source_url=row[9],
                        country=row[10],
                        origin_country=row[11],
                        created_at=row[12]
                    )
                    return jsonify({
                        "success": True,
                        "data": record.__dict__
                    })
            finally:
                if cursor:
                    cursor.close()
                if conn:
                    conn.close()
        else:
            # 默认返回FDA数据
            record = DataRecord.get_by_id(record_id)
            if record:
                return jsonify({
                    "success": True,
                    "data": record.to_dict()
                })
        
        return jsonify({
            "success": False,
            "message": "记录不存在"
        }), 404
    except Exception as e:
        logging.error(f"获取记录详情失败: {e}")
        return jsonify({
            "success": False,
            "message": f"获取记录详情失败: {str(e)}"
        }), 500 

@batches_bp.route('/batches/export/<batch_id>/<task_type>')
def export_batch(batch_id, task_type):
    """导出批次数据（修复中文乱码）"""
    try:
        # 获取批次信息
        batch = DataBatch.get_by_id(batch_id)
        if not batch:
            flash("批次不存在", "danger")
            return redirect(url_for('batches.batch_list'))

        task = TaskConfig.get_by_id(batch.task_id)

        # 创建带 BOM 的字节流对象
        output = io.BytesIO()
        output.write(codecs.BOM_UTF8)  # 添加 UTF-8 BOM 让 Excel 正确识别编码
        csv_file = io.TextIOWrapper(output, encoding='utf-8', newline='')
        writer = csv.writer(csv_file)

        conn = None
        cursor = None

        if task_type == "fda":
            headers = ["信息来源", "任务标题", "任务摘要", "食品类别", "风险因子", "发布日期",
                       "原始链接", "通报国别", "产地国别", "产品名称", "生产企业"]
            writer.writerow(headers)

            try:
                conn = get_connection()
                cursor = conn.cursor()
                cursor.execute(
                    "SELECT source, title, summary, category, risk_factor, publish_date, "
                    "source_url, country, origin_country, product_name, company_name "
                    "FROM capture_data_fda "
                    "WHERE batch_id = :batch_id ORDER BY id",
                    {"batch_id": batch_id}
                )
                for row in cursor.fetchall():
                    writer.writerow(row)
            finally:
                if cursor: cursor.close()
                if conn: conn.close()

        elif task_type == "rybzszh":
            headers = ["信息来源", "任务标题", "任务摘要", "食品类别", "风险因子", "发布日期",
                       "原始链接", "风险项目检测平台-链接", "通报国别", "产地国别", "产品名称",
                       "规格型号", "生产日期/批号", "生产企业", "不合格项目（标准值）", "检验结果"]
            writer.writerow(headers)

            try:
                conn = get_connection()
                cursor = conn.cursor()
                cursor.execute(
                    "SELECT source, title, summary, category, risk_factor, publish_date, "
                    "source_url, risk_platform_url, country, origin_country, product_name, "
                    "spec_model, production_date, company_name, unqualified_item_standard, test_result "
                    "FROM capture_data_rybzszh "
                    "WHERE batch_id = :batch_id ORDER BY id",
                    {"batch_id": batch_id}
                )
                for row in cursor.fetchall():
                    writer.writerow(row)
            finally:
                if cursor: cursor.close()
                if conn: conn.close()

        elif task_type == "wanfang_magazine":
            headers = ["信息来源", "任务标题", "任务摘要", "发布日期", "原始链接", "通报国别", "产地国别"]
            writer.writerow(headers)

            try:
                conn = get_connection()
                cursor = conn.cursor()
                cursor.execute(
                    "SELECT sources, title, summary, publish_date, source_url, country, origin_country "
                    "FROM capture_data_wanfang_magazine "
                    "WHERE batch_id = :batch_id ORDER BY id",
                    {"batch_id": batch_id}
                )
                for row in cursor.fetchall():
                    writer.writerow(row)
            finally:
                if cursor: cursor.close()
                if conn: conn.close()

        else:
            flash("未知的数据类型", "danger")
            return redirect(url_for('batches.batch_detail', batch_id=batch_id))

        # 刷新流并重置指针
        csv_file.flush()
        output.seek(0)

        task_name = task.task_name if task else "unknown"
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        filename = f"{task_name}_{timestamp}.csv"
        encoded_filename = urllib.parse.quote(filename)

        return Response(
            output.getvalue(),
            mimetype="text/csv",
            headers={"Content-Disposition": f"attachment;filename*=UTF-8''{encoded_filename}"}
        )

    except Exception as e:
        logging.error(f"导出批次数据失败: {e}")
        flash(f"导出批次数据失败: {str(e)}", "danger")
        return redirect(url_for('batches.batch_detail', batch_id=batch_id))